<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.extend.mapper.ExtendProcessedMapper">

    <resultMap id="BaseResultMap" type="com.yooa.extend.api.domain.ExtendProcessed">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="beforeid" column="beforeID" jdbcType="INTEGER"/>
        <result property="afterid" column="afterID" jdbcType="INTEGER"/>
        <result property="time" column="time" jdbcType="DATE"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="read" column="read" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,beforeID,afterID,
        time,type,read
    </sql>
</mapper>
