<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.extend.mapper.GoalExtendPackMapper">

    <sql id="Base_Column_List">
        id,title,table_type,
        goal_type,producer_id,producer_name,
        producer_remark,create_time,update_time,
        audit_status,audit_people_id,audit_people_name,
        reason
    </sql>

    <select id="selectGoalExtendPackList" resultType="com.yooa.extend.api.domain.GoalExtendPack">
        SELECT
        <include refid="Base_Column_List"/>
            FROM goal_extend_pack
        <where>
            AND audit_status IS NOT NULL
            <if test="query.tableType != null">
                AND table_type = #{query.tableType}
            </if>
            <if test="query.goalType != null">
                AND goal_type = #{query.goalType}
            </if>
            <if test="query.producerId != null">
                AND producer_id = #{query.producerId}
            </if>
            <if test="query.producerName != null and query.producerName != ''">
                AND producer_name like CONCAT('%',#{query.producerName},'%')
            </if>
            <if test="query.auditPeopleId != null">
                AND audit_people_id = #{query.auditPeopleId}
            </if>
            <if test="query.auditStatus != null">
                AND audit_status = #{query.auditStatus}
            </if>
            <if test="query.auditStatusList != null">
                AND audit_status in
                <foreach collection="query.auditStatusList" item="auditStatus" open="(" close=")" separator=",">
                    #{auditStatus}
                </foreach>
            </if>
            <if test="query.beginTime != null">
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{query.beginTime},'%y%m%d')
            </if>
            <if test="query.endTime != null">
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{query.endTime},'%y%m%d')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectPackList" resultType="com.yooa.extend.api.domain.GoalExtendPack">
        SELECT p.* FROM goal_extend_pack AS p
        LEFT JOIN yooa_system.sys_user AS u ON p.producer_id = u.user_id
        LEFT JOIN yooa_system.sys_dept AS d ON u.dept_id = d.dept_id
        <where>
            <if test="query.auditStatus != null">
                audit_status = #{query.auditStatus}
            </if>
            <if test="query.tableType != null">
                AND table_type = #{query.tableType}
            </if>
            <if test="query.goalType != null">
                AND goal_type = #{query.goalType}
            </if>
            <if test="query.goalId != null and query.goalId != 0">
                AND p.id = #{query.goalId}
            </if>
            <if test="query.beginTime != null">
                AND p.create_time >= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                AND p.create_time &lt;= #{query.endTime}
            </if>
            <if test="query.producerId != null">
                AND u.user_id = #{query.producerId}
            </if>
            <if test="query.deptType != null">
                AND d.dept_type = #{query.deptType}
            </if>
        </where>
    </select>
</mapper>
