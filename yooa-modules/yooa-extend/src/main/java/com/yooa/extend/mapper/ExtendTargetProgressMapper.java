package com.yooa.extend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.extend.api.domain.ExtendTargetProgress;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.dto.ExtendDetailedDto;
import com.yooa.extend.api.domain.query.OperateBusinessQuery;
import com.yooa.extend.api.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExtendTargetProgressMapper extends BaseMapper<ExtendTargetProgress> {

    /**
     * 以部门结构查历史数据
     *
     * @param dto (参数:fieldsName、deptId、beginTime、endTime)
     * @return
     */
    IndexVo index(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查历史数据
     *
     * @param dto (参数:deptId、beginTime、endTime)
     * @return
     */
    IndexAllVo indexAll(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查历史数据根据部门分组
     *
     * @param dto (参数:hierarchy、fieldsName、deptId、beginTime、endTime)
     * @return
     */
    List<IndexDeptGroupVo> indexDeptGroup(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查历史数据根据部门分组
     *
     * @param dto (参数:hierarchy、selType、deptId、beginTime、endTime)
     * @return
     */
    List<IndexDeptGroupAllVo> indexDeptGroupAll(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查历史数据根据用户分组
     *
     * @param dto (参数:fieldsName、deptId、beginTime、endTime)
     * @return
     */
    List<IndexUserGroupVo> indexUserGroup(@Param("dto") CommonalityDto dto);

    /**
     * 以用户结构查历史数据
     *
     * @param dto (参数:userIds、beginTime、endTime)
     * @return
     */
    IndexUserGroupAllVo indexUserAll(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查历史数据根据时间分组
     *
     * @param dto (参数:expType、fieldsName、deptId、beginTime、endTime)
     * @return
     */
    List<IndexTimeGroupVo> indexTimeGroup(@Param("dto") CommonalityDto dto);

    /**
     * 以部门或用户结构查历史数据根据时间分组
     *
     * @param dto (参数:expType、userIds、deptId、beginTime、endTime)
     * @return
     */
    List<IndexTimeGroupAllVo> indexTimeGroupAll(@Param("dto") CommonalityDto dto);

    /**
     * 获取部门及下级部门下的人数
     *
     * @param dto
     * @return
     */
    Long getPeopleNumber(@Param("dto") CommonalityDto dto);

    /**
     * 排行
     *
     * @param dto
     * @return
     */
    List<IndexUserGroupAllVo> selPeopleRanking(@Param("dto") CommonalityDto dto);

    /**
     * 批量新增每日数据汇总
     */
    int insertAll(@Param("list") List<ExtendTargetProgress> list);

    /**
     * 批量修改每日数据汇总
     */
    int updateAll(@Param("list") List<ExtendTargetProgress> list);

    // 查询推广用户数据集成清单(先查权限用户)
    IPage<DetailedVo> detailedUserLimits(Page<?> page, @Param("dto") ExtendDetailedDto dto);

    // 查询推广用户数据集成清单(先查权限部门)
    IPage<DetailedVo> ExtendDetailedDeptLimits(Page<?> page, @Param("dto") ExtendDetailedDto dto);

    // 查询推广用户数据集成清单
    List<DetailedVo> ExtendDetailedUserData(@Param("dto") ExtendDetailedDto dto,
                                            @Param("pdUserIds") String pdUserIds,
                                            @Param("userIds") String userIds);

    // 查询推广部门集完成汇总清单
    List<DetailedVo> ExtendDetailedDeptData(@Param("dto") ExtendDetailedDto dto,
                                            @Param("deptIds") String deptIds);

    IPage<DetailedVo> getOperateUser(Page page,@Param("query") OperateBusinessQuery query);

    List<OperateDetailVo> OperateDetailedUserData(@Param("dto") OperateBusinessQuery dto,
                                                  @Param("pdUserIds") String pdUserIds,
                                                  @Param("userIds") String userIds);

    IPage<OperationalBusinessDataVo> operateDetailedUserLimits(Page page,@Param("dto") OperateBusinessQuery detailedDto);
}




