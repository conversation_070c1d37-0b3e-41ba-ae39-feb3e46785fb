package com.yooa.extend.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.DateUtil;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.RemoteCustomerOrderService;
import com.yooa.crm.api.RemoteFriendService;
import com.yooa.crm.api.domain.vo.FriendRegisterChargeVo;
import com.yooa.extend.api.domain.ExtendTargetProgress;
import com.yooa.extend.api.domain.ExtendTargetProgressHour;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.dto.ExtendDetailedDto;
import com.yooa.extend.api.domain.query.OperateBusinessQuery;
import com.yooa.extend.api.domain.vo.*;
import com.yooa.extend.enums.IndexUserGroupEnum;
import com.yooa.extend.mapper.*;
import com.yooa.extend.service.ExtendService;
import com.yooa.system.api.RemoteDeptService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysUserVo;
import lombok.AllArgsConstructor;
import lombok.Synchronized;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 */
@AllArgsConstructor
@Service
public class ExtendServiceImpl implements ExtendService {


    private final ExtendTargetProgressMapper extendTargetProgressMapper;
    private final ExtendTargetProgressHourMapper targetHourMapper;
    private final ExtendVermicelliMapper vermicelliMapper;
    private final GoalExtendWeeksMapper weeksMapper;
    private final RemoteDeptService remoteDeptService;
    private final RemoteUserService remoteUserService;
    private final RemoteFriendService remoteFriendService;
    private final RemoteCustomerOrderService remoteCustomerOrderService;

    private final GoalExtendPackMapper goalExtendPackMapper;

    @Synchronized
    @Override
    public List<IndexDeptGroupVo> extendPerformanceTotalRanking(CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getHierarchy())) {
            throw new ServiceException("请先选择部门层级!");
        }

        commonalityDto.setFieldsName("real_profit");            // 查询总业绩
        List<IndexDeptGroupVo> groupDeptVoList = CollUtil.newArrayList();
        if (commonalityDto.getBeginTime().toLocalDate().compareTo(LocalDate.now()) >= 0) {             // 判断是不是今天,今天就去查实时数据
            groupDeptVoList = targetHourMapper.indexDeptGroup(commonalityDto);
        } else {     // 查历史数据就行
            groupDeptVoList = extendTargetProgressMapper.indexDeptGroup(commonalityDto);
        }
        // 计算排名
        groupDeptVoList = groupDeptVoList.stream()
                .sorted(Comparator.comparing(IndexDeptGroupVo::getValue).reversed())
                .collect(Collectors.toList());
        for (int i = 0; i < groupDeptVoList.size(); i++) {
            groupDeptVoList.get(i).setRanking(i + 1);
        }

        return groupDeptVoList;      // 排序
    }

    @Override
    public Map<String, Object> extendIndexTimeGroup(CommonalityDto commonalityDto, SysDept sysDept) {
        List<IndexTimeGroupTwoVo> twoVoList = new ArrayList<>();      // 拼接数据
        Map<String, Object> resultMap = new HashMap<>();              // 最终返回

        if (ObjUtil.isEmpty(commonalityDto.getDeptId())) {
            throw new ServiceException("请先选择部门!");
        }

        List<IndexTimeGroupVo> list = CollUtil.newArrayList();               // 根据传来的字段去统计的完成数据(时间分组)(完成)
        if (commonalityDto.selDirection() == 0) {                                 // 查数据汇总
            if (commonalityDto.getBeginTime().toLocalDate().compareTo(LocalDate.now()) >= 0 && commonalityDto.getExpType() == 3) {
                list = targetHourMapper.indexTimeGroup(commonalityDto);          // 判断是不是今天,今天就去查每小时数据汇总
            } else {
                list = extendTargetProgressMapper.indexTimeGroup(commonalityDto);// 不是今天,查每日数据汇总
            }
        } else if (commonalityDto.selDirection() == 2) {                          // 查询粉丝登记列表
            list = vermicelliMapper.indexDeptTimeGroup(commonalityDto);
        } else if (commonalityDto.selDirection() == 1) {                          // 查crm好友、注册
            list = remoteFriendService.getFriendRegisterDeptTimeGroup(commonalityDto.getDeptId(),
                    commonalityDto.getBeginTime().toString(),
                    commonalityDto.getEndTime().toString(),
                    commonalityDto.getExpType(),

                    SecurityConstants.INNER).getData().stream().map(f ->
                    IndexTimeGroupVo.builder()
                            .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(f.getRegister()) :
                                    BigDecimal.valueOf(f.getFriend()))
                            .date(f.getDate().atStartOfDay())
                            .build()
            ).collect(Collectors.toList());
        }

        List<LocalDateTime> dateList = commonalityDto.computeDatePoorList();             // 时间分组

        List<IndexTimeGroupVo> list1 = weeksMapper.spliceGoalData(commonalityDto);       // 根据传来的字段去统计的完成数据(时间分组)(目标)

        if (CollUtil.isNotEmpty(list)) {
            if (commonalityDto.getExpType() == 2 || commonalityDto.getExpType() == 1) {  // 目标是已天为单位所以要平分
                list1.forEach(l -> {
                    Long days = ChronoUnit.DAYS.between(l.getDate(), l.getEndDate()) + 1;     // 获得这个星期的天数
                    if (l.getValue().compareTo(BigDecimal.ZERO) != 0) {
                        l.setValue(l.getValue().divide(BigDecimal.valueOf(days), 2, RoundingMode.HALF_UP));      // 除以天数
                    }
                });
            }

            for (LocalDateTime d : dateList) {
                IndexTimeGroupTwoVo twoVo = new IndexTimeGroupTwoVo();
                twoVo.setCompleteValue(BigDecimal.ZERO);
                twoVo.setGoalValue(BigDecimal.ZERO);
                twoVo.setDate(d);
                for (IndexTimeGroupVo vo : list) {       // 完成
                    if (ObjUtil.isNotNull(vo.getDate()) && vo.getDate().compareTo(twoVo.getDate()) == 0) {
                        twoVo.setCompleteValue(vo.getValue());
                    }
                }
                for (IndexTimeGroupVo vo : list1) {      // 目标
                    if (d.compareTo(vo.getDate()) >= 0 && vo.getEndDate().compareTo(d) >= 0) {
                        twoVo.setGoalValue(vo.getValue());
                    }
                }
                if (commonalityDto.getExpType() == 0) {                                                 // 已月为单位,转换格式
                    twoVo.setDate(d.withDayOfMonth(1));
                }
                twoVoList.add(twoVo);
            }

        }

        resultMap.put("date", new ArrayList<String>());
        resultMap.put("goal", new ArrayList<BigDecimal>());
        resultMap.put("complete", new ArrayList<BigDecimal>());


        // 转换成前端需要的格式返回
        List<String> strs = new ArrayList<>(Arrays.asList(new String[]{"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期天"}));
        // 两个天数相隔一天,并且展示格式为星期,为星期一(特殊处理)
        if (ChronoUnit.DAYS.between(commonalityDto.getBeginTime(), commonalityDto.getEndTime()) == 1 && commonalityDto.getExpType() == 1) {
            List<String> strings = (List<String>) resultMap.get("date");
            strings.add(strs.get(0));       // 时间
            BigDecimal b1 = BigDecimal.ZERO;
            BigDecimal b2 = BigDecimal.ZERO;
            for (IndexTimeGroupTwoVo t : twoVoList) {
                b1 = b1.add(t.getGoalValue());
                b2 = b2.add(t.getCompleteValue());
            }
            List<BigDecimal> bigs1 = (List<BigDecimal>) resultMap.get("goal");
            bigs1.add(b1);
            List<BigDecimal> bigs2 = (List<BigDecimal>) resultMap.get("complete");
            bigs2.add(b2);
        } else {
            for (IndexTimeGroupTwoVo t : twoVoList) {
                List<String> strings = (List<String>) resultMap.get("date");
                if (commonalityDto.getExpType() == 1) {         // 周为单位的话转换一下
                    strings.add(strs.get(0));
                    strs.remove(strs.get(0));
                } else if (commonalityDto.getExpType() == 3) {  // 小时的单位只要小时
                    strings.add(t.getDate().getHour() + "点");
                } else if (commonalityDto.getExpType() == 0) {   // 月只要月份呢
                    strings.add(t.getDate().getMonthValue() + "月");
                } else {
                    strings.add(t.getDate().toString());
                }
                List<BigDecimal> bigs1 = (List<BigDecimal>) resultMap.get("goal");
                bigs1.add(t.getGoalValue());
                List<BigDecimal> bigs2 = (List<BigDecimal>) resultMap.get("complete");
                bigs2.add(t.getCompleteValue());
            }
        }
        return resultMap;
    }

    @Override
    public CompareVo extendIndexCompare(CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getDeptId())) {
            throw new ServiceException("请先选择部门!");
        }
        CompareVo compareVo = new CompareVo();              // 最终返回
        IndexVo vo = new IndexVo();       // 根据传来的字段去统计的完成数据(时间分组)(原完成)

        /**完成的数据统计*/
        if (commonalityDto.selDirection() == 0) {         // 判断是不是今天,今天就去查实时数据
            if (commonalityDto.getBeginTime().toLocalDate().compareTo(LocalDate.now()) >= 0) {
                vo = targetHourMapper.index(commonalityDto);                // 查每小时数据汇总
            } else {
                vo = extendTargetProgressMapper.index(commonalityDto);      // 查询每日数据汇总
            }
        } else if (commonalityDto.selDirection() == 2) {            // 查询粉丝登记列表
            vo = vermicelliMapper.index(commonalityDto);
        } else if (commonalityDto.selDirection() == 1) {      // 查crm好友、注册
            FriendRegisterChargeVo friendVo = remoteFriendService.getFriendRegisterDept(commonalityDto.getDeptId(),
                    null,
                    null,
                    commonalityDto.getBeginTime().toString(),
                    commonalityDto.getEndTime().toString(),
                    SecurityConstants.INNER).getData().get(0);
            vo.setValue(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(friendVo.getRegister()) :
                    BigDecimal.valueOf(friendVo.getFriend()));
            vo.setPeopleNumber(extendTargetProgressMapper.getPeopleNumber(commonalityDto));     // 查询人数
        }

        if (!ObjUtil.isEmpty(vo)) {
            compareVo.setOriginalData(vo.getValue());               // 保存数据到返回类

            if (ObjUtil.isNotNull(commonalityDto.getCompare())) {
                /**查询同比和环比*/
                IndexVo withVo = new IndexVo();   // 根据传来的字段去统计的完成数据(时间分组)(同期完成)
                IndexVo ringVo = new IndexVo();   // 根据传来的字段去统计的完成数据(时间分组)(环期完成)

                commonalityDto.compareInt();       // 同步环比时间数字计算
                /**环比*/
                commonalityDto.ringDate();         // 计算环比时间
                if (commonalityDto.selDirection() == 0) {                            // 查询数据汇总
                    ringVo = extendTargetProgressMapper.index(commonalityDto);
                } else if (commonalityDto.selDirection() == 2) {                      // 查询粉丝登记列表
                    ringVo = vermicelliMapper.index(commonalityDto);
                } else if (commonalityDto.selDirection() == 1) {                      // 查crm好友、注册
                    FriendRegisterChargeVo friendVo = remoteFriendService.getFriendRegisterDept(commonalityDto.getDeptId(),
                            null,
                            null,
                            commonalityDto.getBeginTime().toString(),
                            commonalityDto.getEndTime().toString(),
                            SecurityConstants.INNER).getData().get(0);
                    ringVo.setValue(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(friendVo.getRegister()) :
                            BigDecimal.valueOf(friendVo.getFriend()));
                    ringVo.setPeopleNumber(extendTargetProgressMapper.getPeopleNumber(commonalityDto));     // 查询人数
                }
                commonalityDto.restore();          // 恢复入参时间
                compareVo.setRingCompareData(ringVo.getValue());    // 保存数据到返回类
                /**同比*/
                commonalityDto.withDate();         // 计算同比时间
                if (commonalityDto.getCompare() != 0) {        // 年没有同比
                    if (commonalityDto.selDirection() == 0) {                        // 查询数据汇总
                        withVo = extendTargetProgressMapper.index(commonalityDto);

                    } else if (commonalityDto.selDirection() == 2) {                  // 查询粉丝登记列表
                        withVo = vermicelliMapper.index(commonalityDto);
                    } else if (commonalityDto.selDirection() == 1) {                  // 查crm好友、注册
                        FriendRegisterChargeVo friendVo = remoteFriendService.getFriendRegisterDept(commonalityDto.getDeptId(),
                                null,
                                null,
                                commonalityDto.getBeginTime().toString(),
                                commonalityDto.getEndTime().toString(),
                                SecurityConstants.INNER).getData().get(0);
                        withVo.setValue(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(friendVo.getRegister()) :
                                BigDecimal.valueOf(friendVo.getFriend()));
                        withVo.setPeopleNumber(extendTargetProgressMapper.getPeopleNumber(commonalityDto));     // 查询人数
                    }
                    commonalityDto.restore();          // 恢复入参时间
                    compareVo.setWithCompareData(withVo.getValue()); // 保存数据到返回类
                }
            }

            /**计算人均和日均*/
            if (ObjUtil.isNotNull(vo.getValue()) && vo.getValue().compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal bg1 = vo.getValue();
                BigDecimal bg2 = new BigDecimal(vo.getPeopleNumber());
                int days = LocalDateUtil.daysBetweenRemoveSunday(commonalityDto.getBeginTime().toLocalDate(), commonalityDto.getEndTime().toLocalDate());
                BigDecimal bg3 = new BigDecimal(days == 0 ? 1 : days);                // 天数不能为0
                compareVo.setPerCapita(bg1.divide(bg2, 2, RoundingMode.HALF_UP));         // 人均
                compareVo.setDayCapita(bg1.divide(bg3, 2, RoundingMode.HALF_UP));         // 日均
            } else {
                compareVo.setPerCapita(BigDecimal.ZERO);
                compareVo.setDayCapita(BigDecimal.ZERO);
            }
        } else {
            compareVo.setOriginalData(BigDecimal.ZERO);
            compareVo.setWithCompareScale(BigDecimal.ZERO);
            compareVo.setRingCompareScale(BigDecimal.ZERO);
            compareVo.setPerCapita(BigDecimal.ZERO);
            compareVo.setDayCapita(BigDecimal.ZERO);
        }

        compareVo.compute();       // 计算

        return compareVo;
    }

    @Override
    public List<CompareVo> extendIndexCompareAll(CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getDeptId())) {
            throw new ServiceException("请先选择部门!");
        }

        List<CompareVo> list = new ArrayList<>();      // 最终返回
        IndexAllVo vo = new IndexAllVo();      // 数据汇总对象(原数据)
        /**完成的数据统计*/
        if (commonalityDto.getBeginTime().toLocalDate().compareTo(LocalDate.now()) >= 0) {         // 判断是不是今天,今天就去查实时数据
            vo = targetHourMapper.indexAll(commonalityDto);                          // 查每小时数据汇总
        } else {
            vo = extendTargetProgressMapper.indexAll(commonalityDto);                // 查询每日数据汇总
        }
        IndexAllVo result2 = vermicelliMapper.indexAll(commonalityDto);              // 查询粉丝登记列表
        vo.setFans2h(result2.getFans2h());          // 值过渡
        vo.setFans5h(result2.getFans5h());
        vo.setFans5k(result2.getFans5k());
        vo.setFans5w(result2.getFans5w());
        vo.setFirstCharge(result2.getFirstCharge());

        if (ObjUtil.isNotNull(commonalityDto.getCompare())) {
            /**查询同比和环比**/
            IndexAllVo ringResult = new IndexAllVo();      // 数据汇总对象(环比)
            IndexAllVo withResult = new IndexAllVo();      // 数据汇总对象(同比)

            commonalityDto.compareInt();       // 同步环比时间数字计算
            /**环比*/
            commonalityDto.ringDate();         // 计算环比时间
            ringResult = extendTargetProgressMapper.indexAll(commonalityDto);              // 查询数据汇总
            IndexAllVo ringResult2 = vermicelliMapper.indexAll(commonalityDto);// 查询粉丝登记列表
            commonalityDto.restore();          // 恢复入参时间
            ringResult.setFans2h(ringResult2.getFans2h());          // 值过渡
            ringResult.setFans5h(ringResult2.getFans5h());
            ringResult.setFans5k(ringResult2.getFans5k());
            ringResult.setFans5w(ringResult2.getFans5w());
            ringResult.setFirstCharge(ringResult2.getFirstCharge());
            /**同比*/
            commonalityDto.withDate();         // 计算同比时间
            if (commonalityDto.getCompare() != 0) {      // 年没有同比
                withResult = extendTargetProgressMapper.indexAll(commonalityDto);               // 查询数据汇总
                IndexAllVo withResult2 = vermicelliMapper.indexAll(commonalityDto);// 查询粉丝登记列表
                commonalityDto.restore();          // 恢复入参时间
                withResult.setFans2h(withResult2.getFans2h());          // 值过渡
                withResult.setFans5h(withResult2.getFans5h());
                withResult.setFans5k(withResult2.getFans5k());
                withResult.setFans5w(withResult2.getFans5w());
                withResult.setFirstCharge(withResult2.getFirstCharge());
            }

            Field[] fields = vo.getClass().getDeclaredFields();         // 源数据
            Field[] fields1 = withResult.getClass().getDeclaredFields();    // 同比数据
            Field[] fields2 = ringResult.getClass().getDeclaredFields();    // 环比数据
            BigDecimal bg2 = ObjUtil.isNotNull(vo.getPeopleNumber()) ? BigDecimal.valueOf(vo.getPeopleNumber()) : BigDecimal.ZERO;      // 人数
            BigDecimal bg3 = new BigDecimal(LocalDateUtil.daysBetweenRemoveSunday(commonalityDto.getBeginTime().toLocalDate(), commonalityDto.getEndTime().toLocalDate()));
            /**把这个三个对象中对应的字段相对比 */
            for (int i = 0; i < vo.getClass().getDeclaredFields().length; i++) {
                fields[i].setAccessible(true);      // 开放get权限
                fields1[i].setAccessible(true);
                fields2[i].setAccessible(true);
                CompareVo compareVo = new CompareVo();       // 最总返回
                String name = fields[i].getName();      // 字段名
                if (!name.equals("peopleNumber")) {
                    compareVo.setName(name);
                    compareVo.setId(commonalityDto.getFieldsName(name));
                    try {
                        compareVo.setWithCompareData(new BigDecimal(fields1[i].get(withResult).toString()));
                    } catch (Exception e) {
                        compareVo.setWithCompareData(BigDecimal.ZERO);
                    }
                    try {
                        compareVo.setOriginalData(new BigDecimal(fields[i].get(vo).toString()));
                    } catch (Exception e) {
                        compareVo.setOriginalData(BigDecimal.ZERO);
                    }
                    try {
                        compareVo.setRingCompareData(new BigDecimal(fields2[i].get(ringResult).toString()));
                    } catch (Exception e) {
                        compareVo.setRingCompareData(BigDecimal.ZERO);
                    }
                    compareVo.compute();       // 计算

                    /**计算人均和日均*/
                    if (compareVo.getOriginalData().compareTo(BigDecimal.ZERO) != 0 && bg2.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal bg1 = compareVo.getOriginalData();
                        compareVo.setPerCapita(bg1.divide(bg2, 2, RoundingMode.HALF_UP));
                        compareVo.setDayCapita(bg1.divide(bg3, 2, RoundingMode.HALF_UP));
                    } else {
                        compareVo.setPerCapita(BigDecimal.ZERO);
                        compareVo.setDayCapita(BigDecimal.ZERO);
                    }

                    list.add(compareVo);
                }
            }
        }

        return list;
    }

    @Override
    public ReturnIndexUserGroupVo extendIndexCompareRanking(CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getParentId())) {
            throw new ServiceException("请先选择部门!");
        }
        if (ObjUtil.isEmpty(commonalityDto.getCompare())) {
            throw new ServiceException("缺少参数!");
        }
        List<SysDept> deptList = remoteDeptService.getDeptList(DeptQuery.builder()
                .deptId(commonalityDto.getParentId())
                .build(), SecurityConstants.INNER).getData();
        if (CollUtil.isEmpty(deptList)) {
            throw new ServiceException("没有此部门!");
        }
        SysDept dept = deptList.get(0);     // 入参的部门,用来查询是否是最下级部门

        commonalityDto.humpConvert();           // 驼峰转换
        ReturnIndexUserGroupVo vo = new ReturnIndexUserGroupVo();        // 最终返回(包含结果集)

        /**分辨查询用户还是部门,部门是最底层查用户、其他查部门*/
        if (dept.getLowermost().equals(DictConstants.SYS_DEFAULT_YES)) {     // 最下级部门,查用户
            commonalityDto.setDeptId(commonalityDto.getParentId());
            commonalityDto.setParentId(null);
            if (commonalityDto.selDirection() == 0) {
                if (commonalityDto.getBeginTime().toLocalDate().compareTo(LocalDate.now()) >= 0) {
                    // 查每小时数据汇总
                    vo.setDtoList(targetHourMapper.indexUserGroup(commonalityDto).stream().map(user ->
                            IndexGroupVo.builder()
                                    .id(user.getUserId())
                                    .name(user.getNickName())
                                    .value(user.getValue())
                                    .build()).collect(Collectors.toList()));
                } else {
                    // 查询每日数据汇总
                    vo.setDtoList(extendTargetProgressMapper.indexUserGroup(commonalityDto).stream().map(user ->
                            IndexGroupVo.builder()
                                    .id(user.getUserId())
                                    .name(user.getNickName())
                                    .value(user.getValue())
                                    .build()).collect(Collectors.toList()));
                }
            } else if (commonalityDto.selDirection() == 2) {         // 查询粉丝登记列表
                vo.setDtoList(vermicelliMapper.indexUserGroup(commonalityDto).stream().map(user ->
                        IndexGroupVo.builder()
                                .id(user.getUserId())
                                .name(user.getNickName())
                                .value(user.getValue())
                                .build()).collect(Collectors.toList()));
            } else if (commonalityDto.selDirection() == 1) {    // 查crm好友、注册
                vo.setDtoList(remoteFriendService.getFriendRegister(remoteUserService.getUserList(UserQuery.builder()
                                .deptId(commonalityDto.getParentId())
                                .build(), SecurityConstants.INNER).getData().stream().map(SysUser::getUserId).toList(),
                        commonalityDto.getBeginTime().toString(),
                        commonalityDto.getEndTime().toString(),
                        SecurityConstants.INNER).getData().stream().map(friend ->
                        IndexGroupVo.builder()
                                .id(friend.getId())
                                .name(friend.getName())
                                .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(friend.getRegister()) :
                                        BigDecimal.valueOf(friend.getFriend()))
                                .build()).collect(Collectors.toList()));
            }
        } else {
            if (commonalityDto.selDirection() == 0) {
                if (commonalityDto.getBeginTime().toLocalDate().compareTo(LocalDate.now()) >= 0) {
                    // 查每小时数据汇总
                    vo.setDtoList(targetHourMapper.indexDeptGroup(commonalityDto).stream().map(deptVo ->
                            IndexGroupVo.builder()
                                    .id(deptVo.getDeptId())
                                    .name(deptVo.getDeptName())
                                    .value(deptVo.getValue())
                                    .build()).collect(Collectors.toList()));
                } else {
                    // 查询数据汇总
                    vo.setDtoList(extendTargetProgressMapper.indexDeptGroup(commonalityDto).stream().map(deptVo ->
                            IndexGroupVo.builder()
                                    .id(deptVo.getDeptId())
                                    .name(deptVo.getDeptName())
                                    .value(deptVo.getValue())
                                    .build()).collect(Collectors.toList()));
                }
            } else if (commonalityDto.selDirection() == 2) {         // 查询粉丝登记列表
                vo.setDtoList(vermicelliMapper.indexDeptGroup(commonalityDto).stream().map(deptVo ->
                        IndexGroupVo.builder()
                                .id(deptVo.getDeptId())
                                .name(deptVo.getDeptName())
                                .value(deptVo.getValue())
                                .build()).collect(Collectors.toList()));
            } else if (commonalityDto.selDirection() == 1) {    // 查crm好友、注册
                vo.setDtoList(remoteFriendService.getFriendRegisterDept(null,
                        commonalityDto.getParentId(),
                        null,
                        commonalityDto.getBeginTime().toString(),
                        commonalityDto.getEndTime().toString(),
                        SecurityConstants.INNER).getData().stream().map(friend ->
                        IndexGroupVo.builder()
                                .id(friend.getId())
                                .name(friend.getName())
                                .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(friend.getRegister()) :
                                        BigDecimal.valueOf(friend.getFriend()))
                                .build()).collect(Collectors.toList()));
            }
        }

        // 计算排名
        vo.setDtoList(vo.getDtoList().stream()
                .sorted(Comparator.comparing(IndexGroupVo::getValue).reversed())
                .collect(Collectors.toList()));
        for (int i = 0; i < vo.getDtoList().size(); i++) {
            vo.getDtoList().get(i).setRanking(i + 1);
        }

        // 计算人均
        vo.setTotal(vo.getDtoList().stream().map(IndexGroupVo::getValue).reduce(BigDecimal.ZERO, BigDecimal::add));     // 总和
        BigDecimal bg1 = vo.getTotal();                             // 总和
        BigDecimal bg2 = new BigDecimal(vo.getDtoList().size());    // 人数
        if (vo.getTotal().compareTo(BigDecimal.ZERO) != 0) {
            vo.setCapita(bg1.divide(bg2, 4, RoundingMode.HALF_UP));    // 人均
        } else {
            vo.setCapita(BigDecimal.ZERO);
        }

        if (ObjUtil.isNotNull(commonalityDto.getCompare())) {
            commonalityDto.computeForward();        // 推前时间数字计算

            /**查询以前的数据*/
            commonalityDto.forwardDate();           // 推前时间
            List<IndexGroupVo> beforeVoList = CollUtil.newArrayList();               // 以前数据的集合
            if (dept.getLowermost().equals(DictConstants.SYS_DEFAULT_YES)) {     // 最下级部门,查用户
                if (commonalityDto.selDirection() == 0) {            // 查询数据汇总
                    beforeVoList = extendTargetProgressMapper.indexUserGroup(commonalityDto).stream().map(user ->
                            IndexGroupVo.builder()
                                    .id(user.getUserId())
                                    .name(user.getNickName())
                                    .value(user.getValue())
                                    .build()).collect(Collectors.toList());
                } else if (commonalityDto.selDirection() == 2) {     // 查询粉丝登记
                    beforeVoList = vermicelliMapper.indexUserGroup(commonalityDto).stream().map(user ->
                            IndexGroupVo.builder()
                                    .id(user.getUserId())
                                    .name(user.getNickName())
                                    .value(user.getValue())
                                    .build()).collect(Collectors.toList());          // 查询粉丝登记列表
                } else if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
                    beforeVoList = remoteFriendService.getFriendRegister(remoteUserService.getUserList(UserQuery.builder()
                                    .deptId(commonalityDto.getParentId())
                                    .build(), SecurityConstants.INNER).getData().stream().map(SysUser::getUserId).toList(),
                            commonalityDto.getBeginTime().toString(),
                            commonalityDto.getEndTime().toString(),
                            SecurityConstants.INNER).getData().stream().map(friend ->
                            IndexGroupVo.builder()
                                    .id(friend.getId())
                                    .name(friend.getName())
                                    .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(friend.getRegister()) :
                                            BigDecimal.valueOf(friend.getFriend()))
                                    .build()).collect(Collectors.toList());
                }
            } else {
                if (commonalityDto.selDirection() == 0) {            // 查询数据汇总
                    beforeVoList = extendTargetProgressMapper.indexDeptGroup(commonalityDto).stream().map(deptVo ->
                            IndexGroupVo.builder()
                                    .id(deptVo.getDeptId())
                                    .name(deptVo.getDeptName())
                                    .value(deptVo.getValue())
                                    .build()).collect(Collectors.toList());         // 查询数据汇总
                } else if (commonalityDto.selDirection() == 2) {     // 查询粉丝登记
                    beforeVoList = vermicelliMapper.indexDeptGroup(commonalityDto).stream().map(deptVo ->
                            IndexGroupVo.builder()
                                    .id(deptVo.getDeptId())
                                    .name(deptVo.getDeptName())
                                    .value(deptVo.getValue())
                                    .build()).collect(Collectors.toList());          // 查询粉丝登记列表
                } else if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
                    beforeVoList = remoteFriendService.getFriendRegisterDept(null,
                            commonalityDto.getParentId(),
                            null,
                            commonalityDto.getBeginTime().toString(),
                            commonalityDto.getEndTime().toString(),
                            SecurityConstants.INNER).getData().stream().map(friend ->
                            IndexGroupVo.builder()
                                    .id(friend.getId())
                                    .name(friend.getName())
                                    .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(friend.getRegister()) :
                                            BigDecimal.valueOf(friend.getFriend()))
                                    .build()).collect(Collectors.toList());
                }
            }

            commonalityDto.ringDate();              // 还原时间

            // 计算个人数据(个人占比、个人提升)
            for (IndexGroupVo f : vo.getDtoList()) {
                beforeVoList.forEach(f1 -> {
                    if (f.getId().equals(f1.getId())) {     // 个人提升
                        if (f.getValue().compareTo(BigDecimal.ZERO) != 0 && f1.getValue().compareTo(BigDecimal.ZERO) != 0) {
                            f.setUpScale(f.getValue().divide(f1.getValue(), 2, RoundingMode.HALF_UP));      // 对比以前的值
                        } else {
                            f.setUpScale(BigDecimal.ZERO);
                        }
                    }
                });

                if (f.getValue().compareTo(BigDecimal.ZERO) != 0) {
                    f.setScale(f.getValue().multiply(BigDecimal.valueOf(100)).divide(vo.getTotal(), 2, RoundingMode.HALF_UP)); // 个人占比
                } else {
                    f.setScale(BigDecimal.ZERO);
                }
            }

            // 计算以前的人均
            BigDecimal bg3 = beforeVoList.stream().map(IndexGroupVo::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);       // 以前总和
            BigDecimal bg4 = BigDecimal.ZERO;                                 // 以前人均
            if (bg3.compareTo(BigDecimal.ZERO) != 0 && bg2.compareTo(BigDecimal.ZERO) != 0) {
                bg4 = bg3.divide(bg2, 2, RoundingMode.HALF_UP);
            }

            if (vo.getTotal().compareTo(BigDecimal.ZERO) != 0 && bg3.compareTo(BigDecimal.ZERO) != 0) {       // 对比总和提升
                vo.setTotalScale(vo.getTotal().divide(bg3, 2, RoundingMode.HALF_UP));
            } else {
                vo.setTotalScale(BigDecimal.ZERO);
            }

            if (vo.getCapita().compareTo(BigDecimal.ZERO) != 0 && bg4.compareTo(BigDecimal.ZERO) != 0) {       // 对比人均提升
                vo.setCapitaScale(vo.getCapita().divide(bg4, 2, RoundingMode.HALF_UP));
            } else {
                vo.setCapitaScale(BigDecimal.ZERO);
            }
        }

        return vo;
    }

    @Override
    public List<ExtendConvertVo> extendConvert(CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getDeptId())) {
            throw new ServiceException("请先选择部门!");
        }

        IndexAllVo vo = new IndexAllVo();      // 数据汇总对象
        if (commonalityDto.getBeginTime().toLocalDate().compareTo(LocalDate.now()) >= 0) {         // 判断是不是今天,今天就去查实时数据
            vo = targetHourMapper.indexAll(commonalityDto);                  // 查每小时数据汇总
        } else {     // 不是今天的
            vo = extendTargetProgressMapper.indexAll(commonalityDto);        // 查询每日数据汇总
        }
        // 首充从粉丝登记中拿
        IndexVo indexVo = vermicelliMapper.index(commonalityDto);
        if (ObjUtil.isNotNull(indexVo)) {
            vo.setFirstCharge(indexVo.getValue().intValue());
        }

        // 好友、注册从crm拿
        List<FriendRegisterChargeVo> friendRegisterChargeVoList = remoteFriendService.getFriendRegisterDept(commonalityDto.getDeptId(),
                null,
                null,
                commonalityDto.getBeginTime().toString(),
                commonalityDto.getEndTime().toString(),
                SecurityConstants.INNER).getData();
        if (CollUtil.isNotEmpty(friendRegisterChargeVoList)) {
            FriendRegisterChargeVo friendRegisterChargeVo = friendRegisterChargeVoList.get(0);
            if (ObjUtil.isNotNull(friendRegisterChargeVo)) {
                vo.setFriend(friendRegisterChargeVo.getFriend().intValue());
                vo.setRegister(friendRegisterChargeVo.getRegister().intValue());
            }
        }

        List<ExtendConvertVo> dtoList = new ArrayList<>();     // 最总返回
        ExtendConvertVo dto1 = new ExtendConvertVo();         // 新注册：注册/好友
        dto1.setName("register");
        dto1.setValue(vo.getRegister());
        ExtendConvertVo dto2 = new ExtendConvertVo();         // 优质转换：优质/注册
        dto2.setName("quality_users");
        dto2.setValue(vo.getQualityUsers());
        ExtendConvertVo dto3 = new ExtendConvertVo();         // 交接转换：交接/注册
        dto3.setName("receive");
        dto3.setValue(vo.getReceive());
        ExtendConvertVo dto4 = new ExtendConvertVo();         // 二交转换：二交/注册
        dto4.setName("receive_vip");
        dto4.setValue(vo.getReceiveVip());
        ExtendConvertVo dto5 = new ExtendConvertVo();         // 首充转换：首充/注册
        dto5.setName("first_charge");
        dto5.setValue(vo.getFirstCharge());
        dtoList.add(dto1);
        dtoList.add(dto2);
        dtoList.add(dto3);
        dtoList.add(dto4);
        dtoList.add(dto5);

        if (ObjUtil.isNotNull(vo.getFriend()) && ObjUtil.isNotNull(vo.getRegister())
                && vo.getFriend() != 0 && vo.getRegister() != 0) {
            BigDecimal bgd1 = new BigDecimal(vo.getFriend());      // 好友
            BigDecimal bgd2 = new BigDecimal(vo.getRegister());    // 注册
            dto1.setRatio(bgd2.divide(bgd1, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));        // 新注册：注册/好友
            dto1.setResidualRatio(BigDecimal.valueOf(100).subtract(dto1.getRatio()));
            if (vo.getQualityUsers() != 0) {
                BigDecimal bgd3 = new BigDecimal(vo.getQualityUsers());    // 优质
                dto2.setRatio(bgd3.divide(bgd2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));    // 优质转换：优质/注册
                dto2.setResidualRatio(BigDecimal.valueOf(100).subtract(dto2.getRatio()));
            } else {
                dto2.setRatio(BigDecimal.ZERO);
                dto2.setResidualRatio(BigDecimal.valueOf(100).subtract(dto2.getRatio()));
            }
            if (vo.getReceive() != 0) {
                BigDecimal bgd3 = new BigDecimal(vo.getReceive());    // 交接
                dto3.setRatio(bgd3.divide(bgd2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));    // 交接转换：交接/注册
                dto3.setResidualRatio(BigDecimal.valueOf(100).subtract(dto3.getRatio()));
            } else {
                dto3.setRatio(BigDecimal.ZERO);
                dto3.setResidualRatio(BigDecimal.valueOf(100).subtract(dto3.getRatio()));
            }
            if (vo.getReceiveVip() != 0) {
                BigDecimal bgd3 = new BigDecimal(vo.getReceiveVip());    // 二交
                dto4.setRatio(bgd3.divide(bgd2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));    // 二交转换：二交/注册
                dto4.setResidualRatio(BigDecimal.valueOf(100).subtract(dto4.getRatio()));
            } else {
                dto4.setRatio(BigDecimal.ZERO);
                dto4.setResidualRatio(BigDecimal.valueOf(100).subtract(dto4.getRatio()));
            }
            if (vo.getFirstCharge() != 0) {
                BigDecimal bgd3 = new BigDecimal(vo.getFirstCharge());    // 首充
                dto5.setRatio(bgd3.divide(bgd2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));    // 首充转换：首充/注册
                dto5.setResidualRatio(BigDecimal.valueOf(100).subtract(dto5.getRatio()));
            } else {
                dto5.setRatio(BigDecimal.ZERO);
                dto5.setResidualRatio(BigDecimal.valueOf(100).subtract(dto5.getRatio()));
            }
        } else {
            dto1.setRatio(BigDecimal.ZERO);
            dto1.setResidualRatio(BigDecimal.valueOf(100).subtract(dto1.getRatio()));
            dto2.setRatio(BigDecimal.ZERO);
            dto2.setResidualRatio(BigDecimal.valueOf(100).subtract(dto2.getRatio()));
            dto3.setRatio(BigDecimal.ZERO);
            dto3.setResidualRatio(BigDecimal.valueOf(100).subtract(dto3.getRatio()));
            dto4.setRatio(BigDecimal.ZERO);
            dto4.setResidualRatio(BigDecimal.valueOf(100).subtract(dto4.getRatio()));
            dto5.setRatio(BigDecimal.ZERO);
            dto5.setResidualRatio(BigDecimal.valueOf(100).subtract(dto5.getRatio()));
        }

        return dtoList;
    }

    @Override
    public ExtendConvertGraphVo extendConvertGraph(CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getDeptId())) {
            throw new ServiceException("请先选择部门!");
        } else if (ObjUtil.isEmpty(commonalityDto.getExpType())) {
            throw new ServiceException("参数有误,未传分组类型!");
        } else if (commonalityDto.getExpType() == 3) {
            throw new ServiceException("参数有误,不能根据小时分组!");
        } else if (StrUtil.isEmpty(commonalityDto.getFieldsName())) {
            throw new ServiceException("参数有误,未传统计字段!");
        }

        List<LocalDate> dateList = commonalityDto.computeDatePoorList().stream().map(d -> d.toLocalDate()).toList();    // 分组时间集

        List<IndexTimeGroupAllVo> result = extendTargetProgressMapper.indexTimeGroupAll(commonalityDto);                // 数据汇总
        List<FriendRegisterChargeVo> friendList = remoteFriendService.getFriendRegisterDeptTimeGroup(commonalityDto.getDeptId(),
                commonalityDto.getBeginTime().toString(),
                commonalityDto.getEndTime().toString(),
                commonalityDto.getExpType(),
                SecurityConstants.INNER).getData();                                                                     // 好友、注册
        List<IndexTimeGroupVo> vermicelliList = vermicelliMapper.indexDeptTimeGroup(commonalityDto);                    // 首充
        vermicelliList = vermicelliList.stream().filter(cv -> ObjUtil.isNotNull(cv.getDate())).collect(Collectors.toList());

        /**查询以前的数据做对比**/
        commonalityDto.computeForward();            // 推前时间数字计算
        commonalityDto.forwardDate();               // 推前时间
        List<IndexTimeGroupAllVo> ContrastResult = extendTargetProgressMapper.indexTimeGroupAll(commonalityDto);        // 对比数据
        List<FriendRegisterChargeVo> ContrastFriendList = remoteFriendService.getFriendRegisterDeptTimeGroup(commonalityDto.getDeptId(),
                commonalityDto.getBeginTime().toString(),
                commonalityDto.getEndTime().toString(),
                commonalityDto.getExpType(),
                SecurityConstants.INNER).getData();     // 对比好友、注册
        List<IndexTimeGroupVo> ContrastVermicelliList = vermicelliMapper.indexDeptTimeGroup(commonalityDto);            // 首充
        ContrastVermicelliList = ContrastVermicelliList.stream().filter(cv -> ObjUtil.isNotNull(cv.getDate())).collect(Collectors.toList());
        commonalityDto.restore();                   // 还原时间

        ExtendConvertGraphVo vo = new ExtendConvertGraphVo();            // 最终结果返回

        /**按分组类型来分组*/
        String strName = commonalityDto.getFieldsName();        // 转换的字段
        List<BigDecimal> originalValue = new ArrayList<>();     // 源数据对比结果
        List<BigDecimal> comparelValue = new ArrayList<>();     // 对比数据对比结果
        List<String> groupTimeList = new ArrayList<>();         // 返回的时间
        List<String> strs = new ArrayList<>(Arrays.asList(new String[]{"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期天"}));
        for (LocalDate d : dateList) {
            /**组装时间集*/
            if (commonalityDto.getExpType() == 0) {             // 月
                groupTimeList.add(d.getMonthValue() + "月");
            } else if (commonalityDto.getExpType() == 1) {      // 周
                groupTimeList.add(strs.get(0));
                strs.remove(0);
            } else {                                            // 日
                groupTimeList.add(d.toString());
            }
            /**组装源数据对比率*/
            Integer value1 = 0;     // 源数据
            Integer value2 = 0;     // 对比数据
            IndexTimeGroupAllVo indexVo = result.stream().filter(r -> r.getDate().compareTo(d) == 0).findFirst().orElse(null);
            FriendRegisterChargeVo friendVo = new FriendRegisterChargeVo();
            if (CollUtil.isNotEmpty(friendList)) {
                friendVo = friendList.stream().filter(f -> f.getDate().compareTo(d) == 0).findFirst().orElse(null);
            } else {
                friendVo.setFriend(0L);
                friendVo.setRegister(0L);
            }
            IndexTimeGroupVo vermicelli = new IndexTimeGroupVo();
            if (CollUtil.isNotEmpty(vermicelliList)) {
                vermicelli = vermicelliList.stream().filter(v -> v.getDate().toLocalDate().compareTo(d) == 0).findFirst().orElse(null);
            }
            if (ObjUtil.isEmpty(vermicelli) || ObjUtil.isEmpty(vermicelli.getDate())) {
                friendVo.setFirstCharge(0L);
            } else {
                friendVo.setFirstCharge(vermicelli.getValue().longValue());
            }
            if (strName.equals("register")) {                     // 新注册：注册/好友
                value1 = friendVo.getRegister().intValue();
                value2 = friendVo.getFriend().intValue();
            } else if (strName.equals("quality_users")) {         // 优质转换：优质/注册
                value1 = ObjUtil.isNotNull(indexVo) ? indexVo.getQualityUsers() : 0;
                value2 = friendVo.getRegister().intValue();
            } else if (strName.equals("receive")) {               // 交接转换：交接/注册
                value1 = ObjUtil.isNotNull(indexVo) ? indexVo.getReceive() : 0;
                value2 = friendVo.getRegister().intValue();
            } else if (strName.equals("receive_vip")) {           // 二交转换：二交/注册
                value1 = ObjUtil.isNotNull(indexVo) ? indexVo.getReceiveVip() : 0;
                value2 = friendVo.getRegister().intValue();
            } else if (strName.equals("first_charge")) {          // 首充转换：首充/注册
                value1 = ObjUtil.isNotNull(indexVo) ? indexVo.getFirstCharge().intValue() : 0;
                value2 = friendVo.getRegister().intValue();
            }
            if (value1 != 0 && value2 != 0) {
                BigDecimal bgd1 = new BigDecimal(value1);
                BigDecimal bgd2 = new BigDecimal(value2);
                BigDecimal big = bgd1.divide(bgd2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                originalValue.add(big);
            } else {
                originalValue.add(BigDecimal.ZERO);
            }
            /**组装对比数据对比率*/
            Integer value3 = 0;     // 源数据
            Integer value4 = 0;     // 对比数据
            IndexTimeGroupAllVo ContrastIndexVo = new IndexTimeGroupAllVo();
            if (CollUtil.isNotEmpty(ContrastResult)) {
                ContrastIndexVo = ContrastResult.stream().filter(r -> r.getDate().compareTo(d.plusYears(commonalityDto.getYear())
                        .plusMonths(commonalityDto.getMonth()).plusDays(commonalityDto.getDay())) == 0).findFirst().orElse(null);
            }
            FriendRegisterChargeVo ContrastFriendVo = new FriendRegisterChargeVo();
            if (CollUtil.isNotEmpty(ContrastFriendList)) {
                ContrastFriendVo = ContrastFriendList.stream().filter(f -> f.getDate().compareTo(d.plusYears(commonalityDto.getYear())
                        .plusMonths(commonalityDto.getMonth()).plusDays(commonalityDto.getDay())) == 0).findFirst().orElse(null);
            }
            IndexTimeGroupVo ContrastVermicelli = new IndexTimeGroupVo();
            if (CollUtil.isNotEmpty(ContrastVermicelliList)) {
                ContrastVermicelli = ContrastVermicelliList.stream().filter(v -> v.getDate().toLocalDate().compareTo(d) == 0).findFirst().orElse(null);
            }
            if (ObjUtil.isEmpty(ContrastVermicelli) || ObjUtil.isEmpty(ContrastVermicelli.getDate())) {
                ContrastFriendVo.setFirstCharge(0L);
            } else {
                ContrastFriendVo.setFirstCharge(ContrastVermicelli.getValue().longValue());
            }
            if (strName.equals("register")) {                   // 新注册：注册/好友
                value3 = ObjUtil.isNotNull(ContrastFriendVo.getRegister()) ? ContrastFriendVo.getRegister().intValue() : 0;
                value4 = ObjUtil.isNotNull(ContrastFriendVo.getFriend()) ? ContrastFriendVo.getFriend().intValue() : 0;
            } else if (strName.equals("quality_users")) {         // 优质转换：优质/注册
                value3 = ObjUtil.isNotNull(ContrastIndexVo) ? ContrastIndexVo.getQualityUsers() : 0;
                value4 = ObjUtil.isNotNull(ContrastFriendVo.getRegister()) ? ContrastFriendVo.getRegister().intValue() : 0;
            } else if (strName.equals("receive")) {               // 交接转换：交接/注册
                value3 = ObjUtil.isNotNull(ContrastIndexVo) ? ContrastIndexVo.getReceive() : 0;
                value4 = ObjUtil.isNotNull(ContrastFriendVo.getRegister()) ? ContrastFriendVo.getRegister().intValue() : 0;
            } else if (strName.equals("receive_vip")) {           // 二交转换：二交/注册
                value3 = ObjUtil.isNotNull(ContrastIndexVo) ? ContrastIndexVo.getReceiveVip() : 0;
                value4 = ObjUtil.isNotNull(ContrastFriendVo.getRegister()) ? ContrastFriendVo.getRegister().intValue() : 0;
            } else if (strName.equals("first_charge")) {          // 首充转换：首充/注册
                value3 = ObjUtil.isNotNull(ContrastIndexVo) ? ContrastIndexVo.getFirstCharge().intValue() : 0;
                value4 = ObjUtil.isNotNull(ContrastFriendVo.getRegister()) ? ContrastFriendVo.getRegister().intValue() : 0;
            }
            if (value3 != 0 && value4 != 0) {
                BigDecimal bgd1 = new BigDecimal(value3);
                BigDecimal bgd2 = new BigDecimal(value4);
                BigDecimal big = bgd1.divide(bgd2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                comparelValue.add(big);
            } else {
                comparelValue.add(BigDecimal.ZERO);
            }
        }
        vo.setOriginalValue(originalValue);
        vo.setComparelValue(comparelValue);
        vo.setGroupTimeList(groupTimeList);

        return vo;
    }

    private void addToMap(Map<String, List<IndexUserGroupAllVo>> map, String key, IndexUserGroupAllVo vo) {
        map.computeIfAbsent(key, k -> new CopyOnWriteArrayList<>()).add(vo);
    }

    private Stream<PeopleRankingVo> generateRankingVoStream(String fieldType, List<IndexUserGroupAllVo> list) {
        Comparator<IndexUserGroupAllVo> comparator = IndexUserGroupEnum.getComparator(fieldType);
        return list.stream()
                .sorted(comparator.reversed())
                .limit(10)
                .map(vo -> createRankingVo(fieldType, vo));
    }


    private PeopleRankingVo createRankingVo(String fieldType, IndexUserGroupAllVo vo) {
        PeopleRankingVo prVo = new PeopleRankingVo();
        prVo.setId(vo.getUserId());
        prVo.setFieldsName(IndexUserGroupEnum.getFieldName(fieldType));
        prVo.setValue(IndexUserGroupEnum.getFieldValue(fieldType, vo));
        return prVo;
    }

    @Override
    public List<PeopleRankingVo> extendPeopleRanking(CommonalityDto commonalityDto) {
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

//          List<PeopleRankingVo> dtoList = new ArrayList<>();          // 最终返回
        if (ObjUtil.isEmpty(commonalityDto.getDeptId())) {
            throw new ServiceException("请选择部门!");
        }
        if (ObjUtil.isEmpty(commonalityDto.getBeginTime()) || ObjUtil.isEmpty(commonalityDto.getEndTime())) {
            throw new ServiceException("请选择时间范围!");
        }

        // ============== 1. 并行获取数据 ==============
        // 主数据查询
        List<IndexUserGroupAllVo> targetProgressList = CompletableFuture.supplyAsync(() -> extendTargetProgressMapper.selPeopleRanking(commonalityDto), executorService).join();
        List<CompletableFuture<Void>> futureList = Lists.newArrayList();
        /**粉丝登记列表*/
        CompletableFuture<Void> completableFuture1 = CompletableFuture.runAsync(() -> vermicelliUserRanking(targetProgressList, commonalityDto), executorService);
        /**crm的数据排名(好友、注册数)*/
        CompletableFuture<Void> completableFuture2 = CompletableFuture.runAsync(() -> crmUserRanking(targetProgressList, commonalityDto), executorService);
        futureList.add(completableFuture1);
        futureList.add(completableFuture2);
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

        // ============== 2. 异步查询对比数据 ==============
        CompletableFuture<List<IndexUserGroupAllVo>> compareFuture = CompletableFuture.supplyAsync(() -> {
            List<CompletableFuture<Void>> futuresList = new ArrayList<>();
            if (ObjUtil.isNotNull(commonalityDto.getCompare())) {
                // 推前时间数字计算
                commonalityDto.computeForward();
                // 推前时间
                commonalityDto.forwardDate();
                try {
                    // 查询以前的数据
                    List<IndexUserGroupAllVo> targetProgressListCopy = CompletableFuture.supplyAsync(() -> extendTargetProgressMapper.selPeopleRanking(commonalityDto), executorService).join();
                    /**粉丝登记列表*/
                    futuresList.add(CompletableFuture.runAsync(() -> vermicelliUserRanking(targetProgressListCopy, commonalityDto), executorService));
                    // crm的数据排名(好友、注册数)
                    futuresList.add(CompletableFuture.runAsync(() -> crmUserRanking(targetProgressListCopy, commonalityDto), executorService));
                    return targetProgressListCopy;
                } finally {
                    CompletableFuture.allOf(futuresList.toArray(new CompletableFuture[futuresList.size()])).join();
                    // 还原时间
                    commonalityDto.restore();
                }
            }
            return Collections.emptyList();
        }, executorService);

        // ============== 3. 数据处理流水线 ==============
        // 3.1 预处理数据分类（减少流操作次数）
        Map<String, List<IndexUserGroupAllVo>> fieldMap = new ConcurrentHashMap<>();
        targetProgressList.parallelStream().forEach(vo -> {
            if (vo.getFriend() != null) addToMap(fieldMap, "friend", vo);
            if (vo.getRegister() != null) addToMap(fieldMap, "register", vo);
            if (vo.getQualityUsers() != null) addToMap(fieldMap, "qualityUsers", vo);
            if (vo.getReceive() != null) addToMap(fieldMap, "receive", vo);
            if (vo.getReceiveVip() != null) addToMap(fieldMap, "receiveVip", vo);
            if (vo.getFirstCharge() != null) addToMap(fieldMap, "firstCharge", vo);
            if (vo.getFortyFiveDays() != null) addToMap(fieldMap, "fortyFiveDays", vo);
            if (vo.getRealProfit() != null) addToMap(fieldMap, "realProfit", vo);
            if (vo.getFirstChargeRate() != null) addToMap(fieldMap, "firstChargeRate", vo);
            if (vo.getRegisterRate() != null) addToMap(fieldMap, "registerRate", vo);
            if (vo.getReceiveRate() != null) addToMap(fieldMap, "receiveRate", vo);
            if (vo.getReceiveVipRate() != null) addToMap(fieldMap, "receiveVipRate", vo);
            if (vo.getFans2h() != null) addToMap(fieldMap, "fans2h", vo);
            if (vo.getFans5k() != null) addToMap(fieldMap, "fans5k", vo);
            if (vo.getFans5w() != null) addToMap(fieldMap, "fans5w", vo);
        });

        // 3.2 并行生成排名数据
        List<PeopleRankingVo> dtoList = fieldMap.entrySet().parallelStream()
                .flatMap(entry -> generateRankingVoStream(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        // ============== 4. 拼接部门 ==============
        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
            if (!dtoList.isEmpty()) {
                Set<Long> userIds = dtoList.stream()
                        .map(PeopleRankingVo::getId)
                        .collect(Collectors.toSet());

                List<SysUserVo> users = remoteUserService.getUserList(
                        UserQuery.builder().userIds(new ArrayList<>(userIds)).build(),
                        SecurityConstants.INNER
                ).getData();

                Map<Long, SysUserVo> userMap = users.stream()
                        .collect(Collectors.toMap(SysUserVo::getUserId, Function.identity()));

                dtoList.forEach(vo -> {
                    SysUserVo user = userMap.get(vo.getId());
                    if (user != null) {
                        vo.setName(user.getNickName());
                        vo.setDeptName(user.getDept().getDeptName());
                    }
                });
            }
        }, executorService);

        // ============== 5. 处理对比数据 ==============
        List<IndexUserGroupAllVo> compareData = compareFuture.join();
        if (!compareData.isEmpty()) {
            Map<Long, IndexUserGroupAllVo> compareMap = compareData.stream()
                    .collect(Collectors.toMap(IndexUserGroupAllVo::getUserId, Function.identity()));

            dtoList.parallelStream().forEach(vo -> {
                IndexUserGroupAllVo compareVo = compareMap.get(vo.getId());
                if (compareVo != null) {
                    IndexUserGroupEnum.setCompareValue(vo, compareVo);
                }
            });
        }
        // ============== 6. 比例计算 ==============
        for (PeopleRankingVo peopleRankingVo : dtoList) {
            if (ObjUtil.isNull(peopleRankingVo.getValue()) || ObjUtil.isNull(peopleRankingVo.getUpValue()) ||
                    peopleRankingVo.getValue().compareTo(BigDecimal.ZERO) <= 0 || peopleRankingVo.getUpValue().compareTo(BigDecimal.ZERO) <= 0) {
                peopleRankingVo.setUpScale("0.0%");
                continue;
            }
            BigDecimal bg1 = peopleRankingVo.getValue();
            BigDecimal bg2 = peopleRankingVo.getUpValue();
            peopleRankingVo.setUpScale(bg1.divide(bg2, 1, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)) + "%");
        }

        //执行拼接部门任务 与其他任务不关联 放最后执行 增加效率
        completableFuture.join();
        //关闭线程池
        executorService.shutdown();
        /**数据汇总*/

//        List<IndexUserGroupAllVo> targetProgressList = extendTargetProgressMapper.selPeopleRanking(commonalityDto);
//        /**粉丝登记列表*/
//        this.vermicelliUserRanking(targetProgressList, commonalityDto);
//        /**crm的数据排名(好友、注册数)*/
//        this.crmUserRanking(targetProgressList, commonalityDto);
//
//        /**把各字段完成的数据排序**/
//        if (CollUtil.isNotEmpty(targetProgressList)) {
//            Integer number = 1;
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFriend())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList1 = targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFriend()))   // 好友排序
//                        .sorted(Comparator.comparingLong(IndexUserGroupAllVo::getFriend).reversed()).limit(10).collect(Collectors.toList());
//                for (IndexUserGroupAllVo f : targetProgressList1) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("好友");
//                    vo.setValue(BigDecimal.valueOf(f.getFriend()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getRegister())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList2 = targetProgressList.stream()            // 注册排序
//                        .sorted(Comparator.comparingLong(IndexUserGroupAllVo::getRegister).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList2) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("注册");
//                    vo.setValue(BigDecimal.valueOf(f.getRegister()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getQualityUsers())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList3 = targetProgressList.stream()            // 优质排序
//                        .sorted(Comparator.comparingInt(IndexUserGroupAllVo::getQualityUsers).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList3) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("优质");
//                    vo.setValue(BigDecimal.valueOf(f.getQualityUsers()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getReceive())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList4 = targetProgressList.stream()            // 接收排序
//                        .sorted(Comparator.comparingInt(IndexUserGroupAllVo::getReceive).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList4) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("交接");
//                    vo.setValue(BigDecimal.valueOf(f.getReceive()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getReceiveVip())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList5 = targetProgressList.stream()            // 二交排序
//                        .sorted(Comparator.comparingInt(IndexUserGroupAllVo::getReceiveVip).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList5) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("二交");
//                    vo.setValue(BigDecimal.valueOf(f.getReceiveVip()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFirstCharge())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList6 = targetProgressList.stream()            // 首充排序
//                        .sorted(Comparator.comparingLong(IndexUserGroupAllVo::getFirstCharge).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList6) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("首充");
//                    vo.setValue(BigDecimal.valueOf(f.getFirstCharge()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFortyFiveDays())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList7 = targetProgressList.stream()            // 新增排序
//                        .sorted(Comparator.comparing(IndexUserGroupAllVo::getFortyFiveDays).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList7) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("新增");
//                    vo.setValue(f.getFortyFiveDays());        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getRealProfit())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList8 = targetProgressList.stream()            // 汇总排序
//                        .sorted(Comparator.comparing(IndexUserGroupAllVo::getRealProfit).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList8) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("总业绩");
//                    vo.setValue(f.getRealProfit());        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            // 付费率：首充/注册
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFirstChargeRate())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList9 = targetProgressList.stream()
//                        .sorted(Comparator.comparing(IndexUserGroupAllVo::getFirstChargeRate).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList9) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("付费率");
//                    vo.setValue(f.getFirstChargeRate());        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            // 注册率：好友/注册
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getRegisterRate())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList10 = targetProgressList.stream()
//                        .sorted(Comparator.comparing(IndexUserGroupAllVo::getRegisterRate).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList10) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("注册率");
//                    vo.setValue(f.getRegisterRate());        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            // 交接率：交接/优质
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getReceiveRate())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList11 = targetProgressList.stream()
//                        .sorted(Comparator.comparing(IndexUserGroupAllVo::getReceiveRate).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList11) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("交接率");
//                    vo.setValue(f.getReceiveRate());        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            // 二交率：二交/交接
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getReceiveVipRate())).toList())) {
//                List<IndexUserGroupAllVo> targetProgressList12 = targetProgressList.stream()
//                        .sorted(Comparator.comparing(IndexUserGroupAllVo::getReceiveVipRate).reversed()).limit(10).collect(Collectors.toList());
//                number = 1;
//                for (IndexUserGroupAllVo f : targetProgressList12) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(f.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("二交率");
//                    vo.setValue(f.getReceiveVipRate());        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            // 两百粉
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFans2h())).toList())) {
//                List<IndexUserGroupAllVo> vermicelliList1 = targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFans2h()))
//                        .sorted(Comparator.comparingLong(IndexUserGroupAllVo::getFans2h).reversed()).limit(10).collect(Collectors.toList());        // 两百粉
//                number = 1;
//                for (IndexUserGroupAllVo v : vermicelliList1) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(v.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("两百粉");
//                    vo.setValue(BigDecimal.valueOf(v.getFans2h()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            // 五千粉
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFans5k())).toList())) {
//                List<IndexUserGroupAllVo> vermicelliList2 = targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFans5k()))
//                        .sorted(Comparator.comparingLong(IndexUserGroupAllVo::getFans5k).reversed()).limit(10).collect(Collectors.toList());        // 五千粉
//                number = 1;
//                for (IndexUserGroupAllVo v : vermicelliList2) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(v.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("五千粉");
//                    vo.setValue(BigDecimal.valueOf(v.getFans5k()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            if (CollUtil.isNotEmpty(targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFans5w())).toList())) {
//                List<IndexUserGroupAllVo> vermicelliList3 = targetProgressList.stream().filter(t -> ObjUtil.isNotNull(t.getFans5w()))
//                        .sorted(Comparator.comparingLong(IndexUserGroupAllVo::getFans5w).reversed()).limit(10).collect(Collectors.toList());        // 五万粉
//                number = 1;
//                for (IndexUserGroupAllVo v : vermicelliList3) {
//                    PeopleRankingVo vo = new PeopleRankingVo();
//                    vo.setId(v.getUserId());
//                    vo.setRanking(number);     // 排名
//                    vo.setFieldsName("五万粉");
//                    vo.setValue(BigDecimal.valueOf(v.getFans5w()));        // 数值
//                    dtoList.add(vo);
//                    number++;
//                }
//            }
//
//            /**判断是否要进行跟以前的数据做对比*/
//            if (ObjUtil.isNotNull(commonalityDto.getCompare())) {
//                commonalityDto.computeForward();        // 推前时间数字计算
//                commonalityDto.forwardDate();           // 推前时间
//                List<IndexUserGroupAllVo> targetProgressListCopy = extendTargetProgressMapper.selPeopleRanking(commonalityDto);     // 查询以前的数据
//                /**粉丝登记列表*/
//                this.vermicelliUserRanking(targetProgressListCopy, commonalityDto);
//                // crm的数据排名(好友、注册数)
//                this.crmUserRanking(targetProgressListCopy, commonalityDto);
//                commonalityDto.restore();               // 还原时间
//
//                /**添加对比值*/
//                if (CollUtil.isNotEmpty(targetProgressListCopy)) {
//                    dtoList.forEach(d -> {
//                        IndexUserGroupAllVo target = targetProgressListCopy.stream().filter(t -> t.getUserId().equals(d.getId())).findFirst().orElse(null);
//                        if (ObjUtil.isNotNull(target)) {
//                            switch (d.getFieldsName()) {
//                                case "好友" -> d.setUpValue(BigDecimal.valueOf(target.getFriend()));
//                                case "注册" -> d.setUpValue(BigDecimal.valueOf(target.getRegister()));
//                                case "优质" -> d.setUpValue(BigDecimal.valueOf(target.getQualityUsers()));
//                                case "交接" -> d.setUpValue(BigDecimal.valueOf(target.getReceive()));
//                                case "二交" -> d.setUpValue(BigDecimal.valueOf(target.getReceiveVip()));
//                                case "首充" -> d.setUpValue(BigDecimal.valueOf(target.getFirstCharge()));
//                                case "新增" -> d.setUpValue(target.getFortyFiveDays());
//                                case "总业绩" -> d.setUpValue(target.getRealProfit());
//                                case "付费率" -> d.setUpValue(target.getFirstChargeRate());
//                                case "注册率" -> d.setUpValue(target.getRegisterRate());
//                                case "交接率" -> d.setUpValue(target.getReceiveRate());
//                                case "二交率" -> d.setUpValue(target.getReceiveVipRate());
//                                case "两百粉" ->
//                                        d.setUpValue(ObjUtil.isNotNull(target.getFans2h()) ? BigDecimal.valueOf(target.getFans2h()) : BigDecimal.ZERO);
//                                case "五千粉" ->
//                                        d.setUpValue(ObjUtil.isNotNull(target.getFans5k()) ? BigDecimal.valueOf(target.getFans5k()) : BigDecimal.ZERO);
//                                case "五万粉" ->
//                                        d.setUpValue(ObjUtil.isNotNull(target.getFans5w()) ? BigDecimal.valueOf(target.getFans5w()) : BigDecimal.ZERO);
//                            }
//                        }
//                    });
//                }
//            }
//        }
//
//        /**给他们拼接部门*/
//        if (!dtoList.isEmpty()) {
//            List<Long> userIds = dtoList.stream().map(PeopleRankingVo::getId).toList();      // 收集用户id集
//            List<SysUserVo> userVoList = remoteUserService.getUserList(UserQuery.builder().userIds(userIds).build(), SecurityConstants.INNER).getData();
//
//            dtoList.forEach(d -> {
//                SysUserVo user = userVoList.stream().filter(u -> u.getUserId().intValue() == d.getId()).findFirst().orElse(null);
//                if (ObjUtil.isNotNull(user)) {
//                    d.setFriendName(user.getNickName());
//                    d.setDeptName(user.getDept().getDeptName());
//                }
//            });
//        }
//
//        /**算比例*/
//        for (PeopleRankingVo peopleRankingVo : dtoList) {
//            if (ObjUtil.isNull(peopleRankingVo.getValue()) || ObjUtil.isNull(peopleRankingVo.getUpValue()) ||
//                    peopleRankingVo.getValue().compareTo(BigDecimal.ZERO) <= 0 || peopleRankingVo.getUpValue().compareTo(BigDecimal.ZERO) <= 0) {
//                peopleRankingVo.setUpScale("0.0%");
//                continue;
//            }
//            BigDecimal bg1 = peopleRankingVo.getValue();
//            BigDecimal bg2 = peopleRankingVo.getUpValue();
//            peopleRankingVo.setUpScale(bg1.divide(bg2, 1, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)) + "%");
//        }
        return dtoList;
    }

    // 查询crm的数据(type:0个人、1部门)
    public void crmUserRanking(List<IndexUserGroupAllVo> targetProgressList, CommonalityDto commonalityDto) {
        Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterRanking(CollUtil.newArrayList(),
                LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                SecurityConstants.INNER).getData();

        // 数据过渡
        if (ObjUtil.isNotNull(map)) {
            List<FriendRegisterChargeVo> friendRanking = map.get("好友排名");                // 好友排名
            List<FriendRegisterChargeVo> registerRanking = map.get("注册排名");              // 注册排名
            for (IndexUserGroupAllVo targetProgress : targetProgressList) {
                if (CollUtil.isNotEmpty(friendRanking)) {        // 过渡好友数据
                    FriendRegisterChargeVo vo = friendRanking.stream().filter(f -> f.getId().equals(targetProgress.getUserId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(vo)) {
                        targetProgress.setFriend(vo.getFriend());
                    } else {
                        targetProgress.setFriend(0L);
                    }
                } else {
                    targetProgress.setFriend(0L);
                }
                if (CollUtil.isNotEmpty(registerRanking)) {      // 过渡注册数据
                    FriendRegisterChargeVo vo = registerRanking.stream().filter(r -> r.getId().equals(targetProgress.getUserId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(vo)) {
                        targetProgress.setRegister(vo.getRegister());
                    } else {
                        targetProgress.setRegister(0L);
                    }
                } else {
                    targetProgress.setRegister(0L);
                }
                /**计算首充率、注册率**/
                if (ObjUtil.isNotNull(targetProgress.getFriend()) && ObjUtil.isNotNull(targetProgress.getRegister())
                        && targetProgress.getFriend() > 0 && targetProgress.getRegister() > 0) {        // 注册率：好友/注册
                    BigDecimal big1 = new BigDecimal(targetProgress.getFriend());
                    BigDecimal big2 = new BigDecimal(targetProgress.getRegister());
                    targetProgress.setRegisterRate(big1.divide(big2, 2, RoundingMode.HALF_UP));
                } else {
                    targetProgress.setRegisterRate(BigDecimal.ZERO);
                }
                if (ObjUtil.isNotNull(targetProgress.getFirstCharge()) && ObjUtil.isNotNull(targetProgress.getRegister())
                        && targetProgress.getFirstCharge() > 0 && targetProgress.getRegister() > 0) {   // 付费率：首充/注册
                    BigDecimal big1 = new BigDecimal(targetProgress.getFirstCharge());
                    BigDecimal big2 = new BigDecimal(targetProgress.getRegister());
                    targetProgress.setFirstChargeRate(big1.divide(big2, 2, RoundingMode.HALF_UP));
                } else {
                    targetProgress.setFirstChargeRate(BigDecimal.ZERO);
                }
            }
        }
    }

    // 查询crm的数据(type:0个人、1部门)
    public void crmDeptRanking(List<IndexDeptGroupAllVo> dtoList, CommonalityDto commonalityDto) {
        Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterDeptRanking(
                commonalityDto.getFindInSetDeptId(),
                commonalityDto.getHierarchy(),
                LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                SecurityConstants.INNER).getData();

        // 数据过渡
        if (ObjUtil.isNotNull(map)) {
            List<FriendRegisterChargeVo> friendRanking = map.get("好友排名");                // 好友排名
            List<FriendRegisterChargeVo> registerRanking = map.get("注册排名");              // 注册排名
            for (IndexDeptGroupAllVo dto : dtoList) {
                if (CollUtil.isNotEmpty(friendRanking)) {        // 过渡好友数据
                    FriendRegisterChargeVo vo = friendRanking.stream().filter(f -> f.getId().equals(dto.getDeptId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(vo)) {
                        dto.setFriend(vo.getFriend());
                    } else {
                        dto.setFriend(0L);
                    }
                } else {
                    dto.setFriend(0L);
                }
                if (CollUtil.isNotEmpty(registerRanking)) {      // 过渡注册数据
                    FriendRegisterChargeVo vo = registerRanking.stream().filter(r -> r.getId().equals(dto.getDeptId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(vo)) {
                        dto.setRegister(vo.getRegister());
                    } else {
                        dto.setRegister(0L);
                    }
                } else {
                    dto.setRegister(0L);
                }
                /**计算首充率、注册率**/
                if (dto.getFriend() > 0 && dto.getRegister() > 0) {        // 注册率：好友/注册
                    BigDecimal big1 = new BigDecimal(dto.getFriend());
                    BigDecimal big2 = new BigDecimal(dto.getRegister());
                    dto.setRegisterRate(big1.divide(big2, 2, RoundingMode.HALF_UP));
                } else {
                    dto.setRegisterRate(BigDecimal.ZERO);
                }
                if (dto.getFirstCharge() > 0 && dto.getRegister() > 0) {   // 付费率：首充/注册
                    BigDecimal big1 = new BigDecimal(dto.getFirstCharge());
                    BigDecimal big2 = new BigDecimal(dto.getRegister());
                    dto.setFirstChargeRate(big1.divide(big2, 2, RoundingMode.HALF_UP));
                } else {
                    dto.setFirstChargeRate(BigDecimal.ZERO);
                }
            }
        }
    }

    // 查询粉丝登记的数据
    public void vermicelliUserRanking(List<IndexUserGroupAllVo> targetProgressList, CommonalityDto commonalityDto) {
        List<IndexUserGroupAllVo> voList = vermicelliMapper.selPeopleRanking(commonalityDto);
        for (IndexUserGroupAllVo targetProgress : targetProgressList) {
            if (CollUtil.isNotEmpty(voList)) {
                IndexUserGroupAllVo vo = voList.stream().filter(v -> v.getUserId().equals(targetProgress.getUserId())).findFirst().orElse(null);
                if (ObjUtil.isNotNull(vo)) {
                    targetProgress.setFans2h(vo.getFans2h());
                    targetProgress.setFans5h(vo.getFans5h());
                    targetProgress.setFans5k(vo.getFans5k());
                    targetProgress.setFans5w(vo.getFans5w());
                    targetProgress.setFirstCharge(vo.getFirstCharge());
                }
            } else {
                targetProgress.setFans2h(0);
                targetProgress.setFans5h(0);
                targetProgress.setFans5k(0);
                targetProgress.setFans5w(0);
                targetProgress.setFirstCharge(0);
            }
        }
    }

    public void vermicelliDeptRanking(List<IndexDeptGroupAllVo> dtoList, CommonalityDto commonalityDto) {
        List<IndexDeptGroupAllVo> voList = vermicelliMapper.indexDeptGroupAll(commonalityDto);
        for (IndexDeptGroupAllVo dto : dtoList) {
            if (CollUtil.isNotEmpty(voList)) {
                IndexDeptGroupAllVo vo = voList.stream().filter(v -> v.getDeptId().equals(dto.getDeptId())).findFirst().orElse(null);
                if (ObjUtil.isNotNull(vo)) {
                    dto.setFans2h(vo.getFans2h());
                    dto.setFans5h(vo.getFans5h());
                    dto.setFans5k(vo.getFans5k());
                    dto.setFans5w(vo.getFans5w());
                    dto.setFirstCharge(vo.getFirstCharge());
                }
            } else {
                dto.setFans2h(0);
                dto.setFans5h(0);
                dto.setFans5k(0);
                dto.setFans5w(0);
                dto.setFirstCharge(0);
            }
        }
    }

    @Override
    public List<DeptRankingVo> extendDeptRanking(CommonalityDto commonalityDto) {
        List<DeptRankingVo> rankingVos = new ArrayList<>();       // 最终返回
        if (ObjUtil.isEmpty(commonalityDto.getHierarchy())) {
            throw new ServiceException("请先选择部门等级!");
        }

        /**数据汇总*/
        List<IndexDeptGroupAllVo> dtoList = extendTargetProgressMapper.indexDeptGroupAll(commonalityDto);
        /**粉丝登记列表*/
        this.vermicelliDeptRanking(dtoList, commonalityDto);
        /**crm的数据排名(好友、注册数)*/
        this.crmDeptRanking(dtoList, commonalityDto);

        /**把各字段完成的数据排序**/
        if (CollUtil.isNotEmpty(dtoList)) {

            Integer number = 1;
            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getFriend())).toList())) {
                List<IndexDeptGroupAllVo> dtoList1 = dtoList.stream()            // 好友排序
                        .sorted(Comparator.comparingLong(IndexDeptGroupAllVo::getFriend).reversed()).limit(10).toList();
                for (IndexDeptGroupAllVo f : dtoList1) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("好友");
                    vo.setValue(BigDecimal.valueOf(f.getFriend()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getRegister())).toList())) {
                List<IndexDeptGroupAllVo> dtoList2 = dtoList.stream()            // 注册排序
                        .sorted(Comparator.comparingLong(IndexDeptGroupAllVo::getRegister).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList2) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("注册");
                    vo.setValue(BigDecimal.valueOf(f.getRegister()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getQualityUsers())).toList())) {
                List<IndexDeptGroupAllVo> dtoList3 = dtoList.stream()            // 优质排序
                        .sorted(Comparator.comparingInt(IndexDeptGroupAllVo::getQualityUsers).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList3) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("优质");
                    vo.setValue(BigDecimal.valueOf(f.getQualityUsers()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getReceive())).toList())) {
                List<IndexDeptGroupAllVo> dtoList4 = dtoList.stream()            // 接收排序
                        .sorted(Comparator.comparingInt(IndexDeptGroupAllVo::getReceive).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList4) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("交接");
                    vo.setValue(BigDecimal.valueOf(f.getReceive()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getReceiveVip())).toList())) {
                List<IndexDeptGroupAllVo> dtoList5 = dtoList.stream()            // 二交排序
                        .sorted(Comparator.comparingInt(IndexDeptGroupAllVo::getReceiveVip).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList5) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("二交");
                    vo.setValue(BigDecimal.valueOf(f.getReceiveVip()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getFirstCharge())).toList())) {
                List<IndexDeptGroupAllVo> dtoList6 = dtoList.stream()            // 首充排序
                        .sorted(Comparator.comparingInt(IndexDeptGroupAllVo::getFirstCharge).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList6) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("首充");
                    vo.setValue(BigDecimal.valueOf(f.getFirstCharge()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getFortyFiveDays())).toList())) {
                List<IndexDeptGroupAllVo> dtoList7 = dtoList.stream()            // 新增排序
                        .sorted(Comparator.comparing(IndexDeptGroupAllVo::getFortyFiveDays).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList7) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("新增");
                    vo.setValue(f.getFortyFiveDays());        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getRealProfit())).toList())) {
                List<IndexDeptGroupAllVo> dtoList8 = dtoList.stream()            // 汇总排序
                        .sorted(Comparator.comparing(IndexDeptGroupAllVo::getRealProfit).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList8) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("总业绩");
                    vo.setValue(f.getRealProfit());        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            // 付费率：首充/注册
            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getFirstChargeRate())).toList())) {
                List<IndexDeptGroupAllVo> dtoList9 = dtoList.stream()
                        .sorted(Comparator.comparing(IndexDeptGroupAllVo::getFirstChargeRate).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList9) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("付费率");
                    vo.setValue(f.getFirstChargeRate());        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            // 注册率：好友/注册
            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getRegisterRate())).toList())) {
                List<IndexDeptGroupAllVo> dtoList10 = dtoList.stream()
                        .sorted(Comparator.comparing(IndexDeptGroupAllVo::getRegisterRate).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList10) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("注册率");
                    vo.setValue(f.getRegisterRate());        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            // 交接率：交接/优质
            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getReceiveRate())).toList())) {
                List<IndexDeptGroupAllVo> dtoList11 = dtoList.stream()
                        .sorted(Comparator.comparing(IndexDeptGroupAllVo::getReceiveRate).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList11) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("交接率");
                    vo.setValue(f.getReceiveRate());        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            // 二交率：二交/交接
            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getReceiveVipRate())).toList())) {
                List<IndexDeptGroupAllVo> dtoList12 = dtoList.stream()
                        .sorted(Comparator.comparing(IndexDeptGroupAllVo::getReceiveVipRate).reversed()).limit(10).toList();
                number = 1;
                for (IndexDeptGroupAllVo f : dtoList12) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(f.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("二交率");
                    vo.setValue(f.getReceiveVipRate());        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            // 两百粉
            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getFans2h())).toList())) {
                List<IndexDeptGroupAllVo> vermicelliList1 = dtoList.stream()
                        .sorted(Comparator.comparingLong(IndexDeptGroupAllVo::getFans2h).reversed()).limit(10).toList();        // 两百粉
                number = 1;
                for (IndexDeptGroupAllVo v : vermicelliList1) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(v.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("两百粉");
                    vo.setValue(BigDecimal.valueOf(v.getFans2h()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            // 五千粉
            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getFans5k())).toList())) {
                List<IndexDeptGroupAllVo> vermicelliList2 = dtoList.stream()
                        .sorted(Comparator.comparingLong(IndexDeptGroupAllVo::getFans5k).reversed()).limit(10).toList();        // 五千粉
                number = 1;
                for (IndexDeptGroupAllVo v : vermicelliList2) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(v.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("五千粉");
                    vo.setValue(BigDecimal.valueOf(v.getFans5k()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }

            if (CollUtil.isNotEmpty(dtoList.stream().filter(t -> ObjUtil.isNotNull(t.getFans5w())).toList())) {
                List<IndexDeptGroupAllVo> vermicelliList3 = dtoList.stream()
                        .sorted(Comparator.comparingLong(IndexDeptGroupAllVo::getFans5w).reversed()).limit(10).toList();        // 五万粉
                number = 1;
                for (IndexDeptGroupAllVo v : vermicelliList3) {
                    DeptRankingVo vo = new DeptRankingVo();
                    vo.setDeptId(v.getDeptId());
                    vo.setRanking(number);     // 排名
                    vo.setFieldsName("五万粉");
                    vo.setValue(BigDecimal.valueOf(v.getFans5w()));        // 数值
                    rankingVos.add(vo);
                    number++;
                }
            }
        }

        /**判断是否要进行跟以前的数据做对比*/
        if (ObjUtil.isNotNull(commonalityDto.getCompare())) {
            commonalityDto.computeForward();        // 推前时间数字计算
            commonalityDto.forwardDate();           // 推前时间
            List<IndexDeptGroupAllVo> dtoListCopy = extendTargetProgressMapper.indexDeptGroupAll(commonalityDto);
            /**粉丝登记列表*/
            this.vermicelliDeptRanking(dtoList, commonalityDto);
            /**crm的数据排名(好友、注册数)*/
            this.crmDeptRanking(dtoList, commonalityDto);
            commonalityDto.restore();               // 还原时间

            /**添加对比值*/
            if (CollUtil.isNotEmpty(dtoListCopy)) {
                rankingVos.forEach(d -> {
                    IndexDeptGroupAllVo target = dtoListCopy.stream().filter(t -> t.getDeptId().equals(d.getDeptId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(target)) {
                        switch (d.getFieldsName()) {
                            case "好友" -> d.setUpValue(BigDecimal.valueOf(target.getFriend()));
                            case "注册" -> d.setUpValue(BigDecimal.valueOf(target.getRegister()));
                            case "优质" -> d.setUpValue(BigDecimal.valueOf(target.getQualityUsers()));
                            case "交接" -> d.setUpValue(BigDecimal.valueOf(target.getReceive()));
                            case "二交" -> d.setUpValue(BigDecimal.valueOf(target.getReceiveVip()));
                            case "首充" -> d.setUpValue(BigDecimal.valueOf(target.getFirstCharge()));
                            case "新增" -> d.setUpValue(target.getFortyFiveDays());
                            case "总业绩" -> d.setUpValue(target.getRealProfit());
                            case "付费率" -> d.setUpValue(target.getFirstChargeRate());
                            case "注册率" -> d.setUpValue(target.getRegisterRate());
                            case "交接率" -> d.setUpValue(target.getReceiveRate());
                            case "二交率" -> d.setUpValue(target.getReceiveVipRate());
                            case "两百粉" -> d.setUpValue(BigDecimal.valueOf(target.getFans2h()));
                            case "五千粉" -> d.setUpValue(BigDecimal.valueOf(target.getFans5k()));
                            case "五万粉" -> d.setUpValue(BigDecimal.valueOf(target.getFans5w()));
                        }
                    }
                });
            }
        }

        /**算比例*/
        for (DeptRankingVo deptRankingVo : rankingVos) {
            if (deptRankingVo.getValue().compareTo(BigDecimal.ZERO) <= 0 || deptRankingVo.getUpValue().compareTo(BigDecimal.ZERO) <= 0) {
                deptRankingVo.setUpScale("0.0%");
                continue;
            }
            BigDecimal bg1 = deptRankingVo.getValue();
            BigDecimal bg2 = deptRankingVo.getUpValue();
            deptRankingVo.setUpScale(bg1.divide(bg2, 1, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)) + "%");
        }

        // 查询部门负责人
        List<SysUserVo> sysUserVos = remoteUserService.getUserList(UserQuery.builder()    // 查出部门负责人
                .userIds(rankingVos.stream().map(DeptRankingVo::getUserId).toList())
                .build(), SecurityConstants.INNER).getData();

        rankingVos.forEach(r -> {
            SysUserVo sysUserVo = sysUserVos.stream().filter(s -> s.getUserId() == r.getUserId()).findFirst().orElse(null);
            if (sysUserVo != null) {
                r.setNickName(sysUserVo.getNickName());
            }
        });

        return rankingVos;
    }

    @Override
    public UserRankingVo myUserRanking(CommonalityDto commonalityDto, SysUserVo sysUserVo) {
        if (ObjUtil.isEmpty(commonalityDto.getFields())) {
            throw new ServiceException("请选择查询类型!");
        }
        if (ObjUtil.isEmpty(ObjUtil.isEmpty(commonalityDto.getBeginTime()) || ObjUtil.isEmpty(commonalityDto.getEndTime()))) {
            throw new ServiceException("请选择时间范围!");
        }
        List<UserRankingVo> targetProgressList = new ArrayList<>();        // 汇总排行榜

        if (commonalityDto.selDirection() == 1) {                                                                             // 查crm好友、注册、首充
            /**crm的数据排名(好友、注册数)*/
            Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterRanking(CollUtil.newArrayList(),
                    LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                    LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                    SecurityConstants.INNER).getData();
            String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
            for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(r.getId())
                        .nickName(r.getName())
                        .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                BigDecimal.valueOf(r.getFriend()))
                        .build();
                targetProgressList.add(vo);
            }
        } else if (commonalityDto.selDirection() == 0) {                                                                      // 查询数据汇总
            for (IndexUserGroupVo e : extendTargetProgressMapper.indexUserGroup(commonalityDto)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(e.getUserId())
                        .nickName(e.getNickName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        } else {                                  // 查询粉丝登记列表
            /**粉丝登记列表*/
            for (IndexUserGroupVo e : vermicelliMapper.indexUserGroup(commonalityDto)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(e.getUserId())
                        .nickName(e.getNickName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        }

        targetProgressList = targetProgressList.stream()
                .sorted(Comparator.comparing(UserRankingVo::getValue).reversed()).collect(Collectors.toList());
        for (int i = 0; i < targetProgressList.size(); i++) {                       // 计算排名
            targetProgressList.get(i).setRowNum(i + 1);
        }

        BigDecimal totalValue = targetProgressList.stream().map(UserRankingVo::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);        // 计算总值
        UserRankingVo vo = new UserRankingVo();      // 最终返回
        vo.setUserId(sysUserVo.getUserId());
        vo.setTotalValue(totalValue);

        if (totalValue.compareTo(BigDecimal.ZERO) > 0 && CollUtil.isNotEmpty(targetProgressList)) {     // 计算人均值
            BigDecimal bg = totalValue;
            BigDecimal bg1 = new BigDecimal(targetProgressList.size());         // 总人数
            vo.setAvgValue(bg.divide(bg1, 2, RoundingMode.HALF_UP));
        } else {
            vo.setAvgValue(BigDecimal.ZERO);
        }

        targetProgressList.forEach(t -> {     // 查找自己的排名和完成值
            if (sysUserVo.getRoles().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
                // 管理员看所有人的总值,没有排名
                vo.setValue(t.getValue().add(t.getValue()));
            } else {
                // 不是管理员就看自己的
                if (vo.getUserId().toString().equals(t.getUserId().toString())) {
                    vo.setRowNum(t.getRowNum());
                    vo.setValue(t.getValue());
                }
            }
        });

        /**算对比值************************************************************************/

        if (ObjUtil.isNotNull(commonalityDto.getCompare())) {       // 对比
            commonalityDto.computeForward();            // 推前时间数字计算
            commonalityDto.forwardDate();               // 推前时间

            List<UserRankingVo> targetProgressListCopy = new ArrayList<>();        // 对比排名榜

            if (commonalityDto.selDirection() == 1) {                                                                             // 查crm好友、注册、首充
                /**crm的数据排名(好友、注册数)*/
                Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterRanking(CollUtil.newArrayList(),
                        LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                        LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                        SecurityConstants.INNER).getData();
                String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
                for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                    UserRankingVo voCopy = UserRankingVo.builder()
                            .userId(r.getId())
                            .nickName(r.getName())
                            .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                    BigDecimal.valueOf(r.getFriend()))
                            .build();
                    targetProgressListCopy.add(voCopy);
                }
            } else if (commonalityDto.selDirection() == 0) {                                                                      // 查询数据汇总
                for (IndexUserGroupVo e : extendTargetProgressMapper.indexUserGroup(commonalityDto)) {         // 过渡
                    UserRankingVo voCopy = UserRankingVo.builder()
                            .userId(e.getUserId())
                            .nickName(e.getNickName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(voCopy);
                }
            } else {                                                                                                         // 查询粉丝登记列表
                /**粉丝登记列表*/
                for (IndexUserGroupVo e : vermicelliMapper.indexUserGroup(commonalityDto)) {         // 过渡
                    UserRankingVo voCopy = UserRankingVo.builder()
                            .userId(e.getUserId())
                            .nickName(e.getNickName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(voCopy);
                }
            }
            commonalityDto.restore();               // 还原时间
            if (ObjUtil.isNotNull(commonalityDto.getSearchBox())) {                    // 搜索框查询用户名
                targetProgressListCopy = targetProgressListCopy.stream()
                        .filter(t -> t.getNickName().indexOf(commonalityDto.getSearchBox()) != -1).collect(Collectors.toList());
            }
            targetProgressListCopy = targetProgressListCopy.stream()
                    .sorted(Comparator.comparing(UserRankingVo::getValue).reversed()).collect(Collectors.toList());
            for (int i = 0; i < targetProgressListCopy.size(); i++) {                       // 计算排名
                targetProgressListCopy.get(i).setRowNum(i + 1);
            }
            BigDecimal totalValueCopy = targetProgressListCopy.stream().map(UserRankingVo::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);        // 计算总值
            vo.setTotalValue(totalValueCopy);

            if (totalValueCopy.compareTo(BigDecimal.ZERO) > 0 && CollUtil.isNotEmpty(targetProgressListCopy)) {
                BigDecimal bg = totalValueCopy;
                BigDecimal bg1 = new BigDecimal(targetProgressListCopy.size());
                vo.setCompareAvgValue(bg.divide(bg1, 2, BigDecimal.ROUND_HALF_UP));
            } else {
                vo.setCompareAvgValue(BigDecimal.ZERO);
            }

            targetProgressListCopy.forEach(t -> {     // 查找对比的自己的排名和完成值
                if (sysUserVo.getRoles().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
                    // 管理员看所有人的总值,没有排名
                    vo.setValue(t.getValue().add(t.getValue()));
                } else {
                    // 不是管理员就看自己的
                    if (vo.getUserId().toString().equals(t.getUserId().toString())) {
                        vo.setCompareValue(t.getValue());
                    }
                }
            });
        }

        vo.compute();      // 计算
        return vo;
    }

    @Override
    public MyDeptRankingVo myDeptRanking(CommonalityDto commonalityDto, SysUserVo sysUserVo) {
        if (ObjUtil.isEmpty(commonalityDto.getFields())) {
            throw new ServiceException("请选择查询类型!");
        }
        if (ObjUtil.isEmpty(ObjUtil.isEmpty(commonalityDto.getBeginTime()) || ObjUtil.isEmpty(commonalityDto.getEndTime()))) {
            throw new ServiceException("请选择时间范围!");
        }

        // 管理员查看部门层级为部门的级别(如:推广一部),不是管理员查询自己的部门层级
        commonalityDto.setHierarchy(sysUserVo.getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN) ? 3 : sysUserVo.getDept().getHierarchy());

        List<MyDeptRankingVo> targetProgressList = new ArrayList<>();        // 汇总排名榜

        if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册、首充
            /**crm的数据排名(好友、注册数)*/
            Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterDeptRanking(
                    commonalityDto.getFindInSetDeptId(),
                    commonalityDto.getHierarchy(),
                    LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                    LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                    SecurityConstants.INNER).getData();
            String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
            for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(r.getId())
                        .deptName(r.getName())
                        .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                BigDecimal.valueOf(r.getFriend()))
                        .build();
                targetProgressList.add(vo);
            }
        } else if (commonalityDto.selDirection() == 0) {                                                                      // 查询数据汇总
            for (IndexDeptGroupVo e : extendTargetProgressMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(e.getDeptId())
                        .deptName(e.getDeptName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        } else {                                  // 查询粉丝登记列表
            /**粉丝登记列表*/
            for (IndexDeptGroupVo e : vermicelliMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(e.getDeptId())
                        .deptName(e.getDeptName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        }
        targetProgressList = targetProgressList.stream()
                .sorted(Comparator.comparing(MyDeptRankingVo::getValue).reversed()).collect(Collectors.toList());
        for (int i = 0; i < targetProgressList.size(); i++) {                       // 计算排名
            targetProgressList.get(i).setRowNum(i + 1);
        }

        BigDecimal totalValue = targetProgressList.stream().map(MyDeptRankingVo::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);        // 计算总值

        MyDeptRankingVo dto = new MyDeptRankingVo();      // 最终返回
        dto.setDeptId(sysUserVo.getDept().getDeptId());
        dto.setTotalValue(totalValue);

        if (totalValue.compareTo(BigDecimal.ZERO) > 0 && CollUtil.isNotEmpty(targetProgressList)) {     // 计算部门平均
            BigDecimal bg = totalValue;
            BigDecimal bg1 = new BigDecimal(targetProgressList.size());         // 部门数
            dto.setAvgValue(bg.divide(bg1, 2, RoundingMode.HALF_UP));
        } else {
            dto.setAvgValue(BigDecimal.ZERO);
        }

        targetProgressList.forEach(t -> {     // 查找自己部门的排名和完成值
            if (sysUserVo.getRoles().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
                // 管理员看所有人的总值,没有排名
                dto.setValue(dto.getValue().add(t.getValue()));
            } else {
                // 不是管理员就看自己的
                if (dto.getDeptId().toString().equals(t.getDeptId().toString())) {
                    dto.setRowNum(t.getRowNum());
                    dto.setValue(t.getValue());
                }
            }
        });

        /**算对比值************************************************************************/

        if (ObjUtil.isNotNull(commonalityDto.getCompare())) {       // 对比
            commonalityDto.computeForward();            // 推前时间数字计算
            commonalityDto.forwardDate();               // 推前时间

            List<MyDeptRankingVo> targetProgressListCopy = new ArrayList<>();        // 对比排名榜

            if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
                /**crm的数据排名(好友、注册数)*/
                Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterDeptRanking(
                        commonalityDto.getFindInSetDeptId(),
                        commonalityDto.getHierarchy(),
                        LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                        LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                        SecurityConstants.INNER).getData();
                String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
                for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                    MyDeptRankingVo vo = MyDeptRankingVo.builder()
                            .deptId(r.getId())
                            .deptName(r.getName())
                            .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                    BigDecimal.valueOf(r.getFriend()))
                            .build();
                    targetProgressListCopy.add(vo);
                }
            } else if (commonalityDto.selDirection() == 0) {  // 查询数据汇总
                for (IndexDeptGroupVo e : extendTargetProgressMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                    MyDeptRankingVo vo = MyDeptRankingVo.builder()
                            .deptId(e.getDeptId())
                            .deptName(e.getDeptName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(vo);
                }
            } else {                                  // 查询粉丝登记列表
                /**粉丝登记列表*/
                for (IndexDeptGroupVo e : vermicelliMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                    MyDeptRankingVo vo = MyDeptRankingVo.builder()
                            .deptId(e.getDeptId())
                            .deptName(e.getDeptName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(vo);
                }
            }
            commonalityDto.restore();           // 还原时间
            for (int i = 0; i < targetProgressListCopy.size(); i++) {                       // 计算排名
                targetProgressListCopy.get(i).setRowNum(i + 1);
            }

            BigDecimal totalValueCopy = targetProgressListCopy.stream().map(MyDeptRankingVo::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);        // 计算总值
            dto.setTotalValue(totalValueCopy);

            if (totalValueCopy.compareTo(BigDecimal.ZERO) > 0 && CollUtil.isNotEmpty(targetProgressListCopy)) {
                BigDecimal bg = totalValueCopy;
                BigDecimal bg1 = new BigDecimal(targetProgressListCopy.size());
                dto.setCompareAvgValue(bg.divide(bg1, 2, RoundingMode.HALF_UP));
            } else {
                dto.setCompareAvgValue(BigDecimal.ZERO);
            }

            targetProgressListCopy.forEach(t -> {     // 查找对比的自己部门的排名和完成值
                if (sysUserVo.getRoles().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
                    // 管理员看所有人的总值,没有排名
                    dto.setValue(dto.getValue().add(t.getValue()));
                } else {
                    // 不是管理员就看自己的
                    if (dto.getDeptId().toString().equals(t.getDeptId().toString())) {
                        dto.setCompareValue(t.getValue());
                    }
                }
            });
        }

        dto.compute();      // 计算

        return dto;
    }

    @Override
    public IPage<UserRankingVoPage> userRankingPage(Page<?> page, CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getFields())) {
            throw new ServiceException("请选择查询类型!");
        }
        if (ObjUtil.isEmpty(ObjUtil.isEmpty(commonalityDto.getBeginTime()) || ObjUtil.isEmpty(commonalityDto.getEndTime()))) {
            throw new ServiceException("请选择时间范围!");
        }
        List<UserRankingVo> targetProgressList = new ArrayList<>();        // 汇总排行榜

        /**先去把排行榜截取出来*/
        if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
            /**crm的数据排名(好友、注册数)*/
            Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterRanking(CollUtil.newArrayList(),
                    LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                    LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                    SecurityConstants.INNER).getData();
            String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
            for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(r.getId())
                        .nickName(r.getName())
                        .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                BigDecimal.valueOf(r.getFriend()))
                        .build();
                targetProgressList.add(vo);
            }
        } else if (commonalityDto.selDirection() == 0) {  // 查询数据汇总
            for (IndexUserGroupVo e : extendTargetProgressMapper.indexUserGroup(commonalityDto)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(e.getUserId())
                        .nickName(e.getNickName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        } else {                                  // 查询粉丝登记列表
            /**粉丝登记列表*/
            for (IndexUserGroupVo e : vermicelliMapper.indexUserGroup(commonalityDto)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(e.getUserId())
                        .nickName(e.getNickName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        }
        if (ObjUtil.isNotNull(commonalityDto.getSearchBox())) {                    // 搜索框查询用户名
            targetProgressList = targetProgressList.stream()
                    .filter(t -> t.getNickName().indexOf(commonalityDto.getSearchBox()) != -1).collect(Collectors.toList());
        }
        targetProgressList = targetProgressList.stream()
                .sorted(Comparator.comparing(UserRankingVo::getValue).reversed()).collect(Collectors.toList());
        for (int i = 0; i < targetProgressList.size(); i++) {                       // 计算排名
            targetProgressList.get(i).setRowNum(i + 1);
        }

        List<UserRankingVoPage> pageList = CollUtil.newArrayList();
        targetProgressList.forEach(t -> {
            UserRankingVoPage voPage = UserRankingVoPage.builder()
                    .userId(t.getUserId())
                    .nickName(t.getNickName())
                    .value(t.getValue())
                    .build();
            pageList.add(voPage);
        });

        /**分页操作**/
        IPage<UserRankingVoPage> iPage = new Page<>();                    // 最终返回结果
        Long l = 0L;            // 起始页
        Long pages = 0L;        // 总页数
        Long tol = 0L;          // 总条数
        if (CollUtil.isNotEmpty(pageList)) {
            tol = pageList.stream().count();
            pages = (long) Math.ceil((double) pageList.size() / page.getSize());
            if (ObjUtil.isNotNull(page)) {
                l = page.getCurrent() - 1;
                if (l > pages) {
                    l = pages;
                }
            }
            iPage.setRecords(pageList.stream()
                    .skip(l * page.getSize())        // 跳过前面的元素
                    .limit(page.getSize())            // 限制返回的元素数量
                    .toList());
        }
        iPage.setPages(pages);
        iPage.setTotal(tol);
        iPage.setSize(page.getSize());
        iPage.setCurrent(page.getCurrent());

        if (ObjUtil.isEmpty(iPage)) {
            return iPage;
        }

        /**
         * 计算对比值(以前的数据)
         */
        if (ObjUtil.isNotNull(commonalityDto.getCompare())) {          // 跟以前的数据作比较
            commonalityDto.computeForward();            // 推前时间数字计算
            commonalityDto.forwardDate();               // 推前时间

            List<UserRankingVo> targetProgressListCopy = new ArrayList<>();        // 对比数据
            if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
                /**crm的数据排名(好友、注册数)*/
                List<FriendRegisterChargeVo> dtoList = remoteFriendService.getFriendRegister(
                        commonalityDto.getUserIds(),
                        LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                        LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                        SecurityConstants.INNER).getData();
                for (FriendRegisterChargeVo r : dtoList) {         // 过渡
                    UserRankingVo vo = UserRankingVo.builder()
                            .userId(r.getId())
                            .nickName(r.getName())
                            .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                    BigDecimal.valueOf(r.getFriend()))
                            .build();
                    targetProgressListCopy.add(vo);
                }
            } else if (commonalityDto.selDirection() == 0) {   // 查询数据汇总
                for (IndexUserGroupVo e : extendTargetProgressMapper.indexUserGroup(commonalityDto)) {         // 过渡
                    UserRankingVo vo = UserRankingVo.builder()
                            .userId(e.getUserId())
                            .nickName(e.getNickName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(vo);
                }
            } else {                                  // 查询粉丝登记列表
                /**粉丝登记列表*/
                for (IndexUserGroupVo e : vermicelliMapper.indexUserGroup(commonalityDto)) {         // 过渡
                    UserRankingVo vo = UserRankingVo.builder()
                            .userId(e.getUserId())
                            .nickName(e.getNickName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(vo);
                }
            }
            commonalityDto.restore();       // 还原时间
            if (ObjUtil.isNotNull(commonalityDto.getSearchBox())) {                    // 搜索框查询用户名
                targetProgressListCopy = targetProgressListCopy.stream()
                        .filter(t -> t.getNickName().indexOf(commonalityDto.getSearchBox()) != -1).collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(targetProgressListCopy)) {       // 作比较
                for (UserRankingVo p : targetProgressListCopy) {
                    UserRankingVoPage vo = iPage.getRecords().stream().filter(t -> t.getUserId().equals(p.getUserId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(vo)) {
                        vo.setCompareValue(p.getValue());
                        if (vo.getValue().compareTo(BigDecimal.ZERO) > 0 && vo.getCompareValue().compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal bg1 = vo.getValue();
                            BigDecimal bg2 = vo.getCompareValue();
                            vo.setScaleValue(bg1.divide(bg2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
                        } else {
                            vo.setScaleValue(BigDecimal.ZERO);
                        }
                    }
                }
            }
        }

        /**要输出的用户,计算总值(不带时间查询)*/
        List<UserRankingVo> targetProgressListCopy = CollUtil.newArrayList();               // 分完页的用户查询总数据,不带时间
        commonalityDto.setBeginTime(null);
        commonalityDto.setEndTime(null);
        commonalityDto.setUserIds(iPage.getRecords().stream().map(UserRankingVoPage::getUserId).toList());
        if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
            /**crm的数据排名(好友、注册数)*/
            List<FriendRegisterChargeVo> dtoList = remoteFriendService.getFriendRegister(
                    commonalityDto.getUserIds(),
                    null,
                    null,
                    SecurityConstants.INNER).getData();
            if (CollUtil.isNotEmpty(dtoList)) {
                for (FriendRegisterChargeVo r : dtoList) {         // 过渡
                    UserRankingVo vo = UserRankingVo.builder()
                            .userId(r.getId())
                            .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                    BigDecimal.valueOf(r.getFriend()))
                            .build();
                    targetProgressListCopy.add(vo);
                }
            }
        } else if (commonalityDto.selDirection() == 0) {                                                                      // 查询数据汇总
            for (IndexUserGroupVo e : extendTargetProgressMapper.indexUserGroup(commonalityDto)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(e.getUserId())
                        .nickName(e.getNickName())
                        .value(e.getValue())
                        .build();
                targetProgressListCopy.add(vo);
            }
        } else {                                  // 查询粉丝登记列表
            /**粉丝登记列表*/
            for (IndexUserGroupVo e : vermicelliMapper.indexUserGroup(commonalityDto)) {         // 过渡
                UserRankingVo vo = UserRankingVo.builder()
                        .userId(e.getUserId())
                        .nickName(e.getNickName())
                        .value(e.getValue())
                        .build();
                targetProgressListCopy.add(vo);
            }
        }

        /**数据进行整合*/
        for (UserRankingVo p : targetProgressListCopy) {
            UserRankingVoPage vo = iPage.getRecords().stream().filter(t -> t.getUserId().equals(p.getUserId())).findFirst().orElse(null);
            if (ObjUtil.isNotNull(vo)) {
                vo.setTotalValue(p.getValue());     // 总值
            }
        }

        // 补充用户部门名

        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            List<Long> userIds = iPage.getRecords().stream().map(UserRankingVoPage::getUserId).toList();
            List<SysUserVo> sysUserVoList = remoteUserService.getUserList(UserQuery.builder()
                    .userIds(userIds)
                    .build(), SecurityConstants.INNER).getData();

            iPage.getRecords().forEach(p -> {
                SysUserVo sysUserVo = sysUserVoList.stream().filter(u -> u.getUserId().equals(p.getUserId())).findFirst().orElse(null);
                p.setDeptName(sysUserVo.getDept().getDeptName());
                p.setAncestorsNames(sysUserVo.getDept().getAncestorsNames());
            });
        }

        return iPage;
    }

    @Override
    public IPage<DeptRankingVoPage> deptRankingPage(Page<?> page, CommonalityDto commonalityDto) {
        if (ObjUtil.isEmpty(commonalityDto.getFields())) {
            throw new ServiceException("请选择查询类型!");
        }
        if (ObjUtil.isEmpty(ObjUtil.isEmpty(commonalityDto.getBeginTime()) || ObjUtil.isEmpty(commonalityDto.getEndTime()))) {
            throw new ServiceException("请选择时间范围!");
        }
        if (ObjUtil.isEmpty(commonalityDto.getHierarchy())) {
            throw new ServiceException("请选择部门层级!");
        }

        if (ObjUtil.isNotNull(commonalityDto.getDeptId())) {         // 切换字段
            commonalityDto.setFindInSetDeptId(commonalityDto.getDeptId());
            commonalityDto.setDeptId(null);
        }

        List<MyDeptRankingVo> targetProgressList = new ArrayList<>();        // 汇总排名榜

        /**先去把排行榜截取出来*/
        if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
            /**crm的数据排名(好友、注册数)*/
            Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterDeptRanking(
                    commonalityDto.getFindInSetDeptId(),
                    commonalityDto.getHierarchy(),
                    LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                    LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                    SecurityConstants.INNER).getData();
            String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
            for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(r.getId())
                        .deptName(r.getName())
                        .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                BigDecimal.valueOf(r.getFriend()))
                        .build();
                targetProgressList.add(vo);
            }
        } else if (commonalityDto.selDirection() == 0) {  // 查询数据汇总
            for (IndexDeptGroupVo e : extendTargetProgressMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(e.getDeptId())
                        .deptName(e.getDeptName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        } else {                                  // 查询粉丝登记列表
            /**粉丝登记列表*/
            for (IndexDeptGroupVo e : vermicelliMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(e.getDeptId())
                        .deptName(e.getDeptName())
                        .value(e.getValue())
                        .build();
                targetProgressList.add(vo);
            }
        }
        if (ObjUtil.isNotNull(commonalityDto.getSearchBox())) {                    // 搜索框查询部门名
            targetProgressList = targetProgressList.stream()
                    .filter(t -> t.getDeptName().indexOf(commonalityDto.getSearchBox()) != -1).collect(Collectors.toList());
        }
        targetProgressList = targetProgressList.stream()
                .sorted(Comparator.comparing(MyDeptRankingVo::getValue).reversed()).collect(Collectors.toList());
        for (int i = 0; i < targetProgressList.size(); i++) {                       // 计算排名
            targetProgressList.get(i).setRowNum(i + 1);
        }

        List<DeptRankingVoPage> pageList = CollUtil.newArrayList();
        targetProgressList.forEach(t -> {
            DeptRankingVoPage voPage = DeptRankingVoPage.builder()
                    .deptId(t.getDeptId())
                    .deptName(t.getDeptName())
                    .value(t.getValue())
                    .build();
            pageList.add(voPage);
        });
        /**分页操作**/
        IPage<DeptRankingVoPage> iPage = new Page<>();          // 最终返回结果
        Long l = 0L;            // 起始页
        Long pages = 0L;        // 总页数
        Long tol = 0L;          // 总条数
        if (CollUtil.isNotEmpty(pageList)) {
            tol = pageList.stream().count();
            pages = (long) Math.ceil((double) pageList.size() / page.getSize());
            if (ObjUtil.isNotNull(page)) {
                l = page.getCurrent() - 1;
                if (l > pages) {
                    l = pages;
                }
            }
            iPage.setRecords(pageList.stream()
                    .skip(l * page.getSize())        // 跳过前面的元素
                    .limit(page.getSize())            // 限制返回的元素数量
                    .toList());
        }
        iPage.setPages(pages);
        iPage.setTotal(tol);
        iPage.setSize(page.getSize());
        iPage.setCurrent(page.getCurrent());

        if (ObjUtil.isEmpty(iPage)) {
            return iPage;
        }

        /**数据进行整合*/
        if (!ObjUtil.isEmpty(commonalityDto.getCompare())) {          // 跟以前的数据作比较
            commonalityDto.computeForward();            // 推前时间数字计算
            commonalityDto.forwardDate();               // 推前时间

            List<MyDeptRankingVo> targetProgressListCopy = new ArrayList<>();        // 对比排名榜

            if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
                /**crm的数据排名(好友、注册数)*/
                Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterDeptRanking(
                        commonalityDto.getFindInSetDeptId(),
                        commonalityDto.getHierarchy(),
                        LocalDateTimeUtil.of(commonalityDto.getBeginTime()).toString(),
                        LocalDateTimeUtil.of(commonalityDto.getEndTime()).toString(),
                        SecurityConstants.INNER).getData();
                String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
                for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                    MyDeptRankingVo vo = MyDeptRankingVo.builder()
                            .deptId(r.getId())
                            .deptName(r.getName())
                            .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                    BigDecimal.valueOf(r.getFriend()))
                            .build();
                    targetProgressListCopy.add(vo);
                }
            } else if (commonalityDto.selDirection() == 0) {                                                                      // 查询数据汇总
                for (IndexDeptGroupVo e : extendTargetProgressMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                    MyDeptRankingVo vo = MyDeptRankingVo.builder()
                            .deptId(e.getDeptId())
                            .deptName(e.getDeptName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(vo);
                }
            } else {                                  // 查询粉丝登记列表
                /**粉丝登记列表*/
                for (IndexDeptGroupVo e : vermicelliMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                    MyDeptRankingVo vo = MyDeptRankingVo.builder()
                            .deptId(e.getDeptId())
                            .deptName(e.getDeptName())
                            .value(e.getValue())
                            .build();
                    targetProgressListCopy.add(vo);
                }
            }
            commonalityDto.restore();           // 还原时间
            if (ObjUtil.isNotNull(commonalityDto.getSearchBox())) {                    // 搜索框查询部门名
                targetProgressListCopy = targetProgressListCopy.stream()
                        .filter(t -> t.getDeptName().indexOf(commonalityDto.getSearchBox()) != -1).collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(targetProgressListCopy)) {       // 作比较
                for (MyDeptRankingVo p : targetProgressListCopy) {
                    DeptRankingVoPage vo = iPage.getRecords().stream().filter(t -> t.getDeptId().equals(p.getDeptId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(vo)) {
                        vo.setCompareValue(p.getValue());
                        if (vo.getValue().compareTo(BigDecimal.ZERO) > 0 && vo.getCompareValue().compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal bg1 = vo.getValue();
                            BigDecimal bg2 = vo.getCompareValue();
                            vo.setScaleValue(bg1.divide(bg2, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
                        } else {
                            vo.setScaleValue(BigDecimal.ZERO);
                        }
                    }
                }
            }
        }

        /**要输出的用户,计算总值(不带时间查询)*/
        List<MyDeptRankingVo> targetProgressListCopy = CollUtil.newArrayList();               // 分完页的部门查询总数据,不带时间
        commonalityDto.setBeginTime(null);
        commonalityDto.setEndTime(null);
        if (commonalityDto.selDirection() == 1) {     // 查crm好友、注册
            /**crm的数据排名(好友、注册数)*/
            Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterDeptRanking(
                    commonalityDto.getFindInSetDeptId(),
                    commonalityDto.getHierarchy(),
                    null,
                    null,
                    SecurityConstants.INNER).getData();
            String str = commonalityDto.getFields() == 0 ? "注册排名" : "好友排名";
            if (ObjUtil.isNotNull(map)) {
                for (FriendRegisterChargeVo r : map.get(str)) {         // 过渡
                    MyDeptRankingVo vo = MyDeptRankingVo.builder()
                            .deptId(r.getId())
                            .deptName(r.getName())
                            .value(commonalityDto.getFields() == 0 ? BigDecimal.valueOf(r.getRegister()) :
                                    BigDecimal.valueOf(r.getFriend()))
                            .build();
                    targetProgressListCopy.add(vo);
                }
            }
        } else if (commonalityDto.selDirection() == 0) {                                                                      // 查询数据汇总
            for (IndexDeptGroupVo e : extendTargetProgressMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(e.getDeptId())
                        .deptName(e.getDeptName())
                        .value(e.getValue())
                        .build();
                targetProgressListCopy.add(vo);
            }
        } else {                                  // 查询粉丝登记列表
            /**粉丝登记列表*/
            for (IndexDeptGroupVo e : vermicelliMapper.indexDeptGroup(commonalityDto)) {         // 过渡
                MyDeptRankingVo vo = MyDeptRankingVo.builder()
                        .deptId(e.getDeptId())
                        .deptName(e.getDeptName())
                        .value(e.getValue())
                        .build();
                targetProgressListCopy.add(vo);
            }
        }

        /**数据进行整合*/
        for (MyDeptRankingVo p : targetProgressListCopy) {
            DeptRankingVoPage vo = iPage.getRecords().stream().filter(t -> t.getDeptId().equals(p.getDeptId())).findFirst().orElse(null);
            if (ObjUtil.isNotNull(vo)) {
                vo.setTotalValue(p.getValue());     // 总值
            }
        }

        // 补充部门负责人、上级部门名拼接
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            List<Long> deptIds = iPage.getRecords().stream().map(DeptRankingVoPage::getDeptId).toList();
            List<SysDept> sysDeptList = remoteDeptService.getDeptList(DeptQuery.builder()
                    .deptIds(deptIds)
                    .build(), SecurityConstants.INNER).getData();
            iPage.getRecords().forEach(p -> {
                SysDept sysDept = sysDeptList.stream().filter(d -> d.getDeptId().equals(p.getDeptId())).findFirst().orElse(null);
                p.setAncestorsNames(sysDept.getAncestorsNames());
                p.setUserId(sysDept.getLeader());
                p.setNickName(sysDept.getLeaderName());
            });
        }

        return iPage;
    }

    @Override
    public WorkbenchMyUserRankingVo workbenchMyUserRanking(Page<?> page, CommonalityDto commonalityDto, SysUserVo sysUserVo) {
        if (ObjUtil.isEmpty(commonalityDto.getBeginTime()) || ObjUtil.isEmpty(commonalityDto.getEndTime())) {
            throw new ServiceException("去选择时间范围!");
        }
        if (ObjUtil.isEmpty(commonalityDto.getSelType())) {
            throw new ServiceException("请选择部门类型!");
        }
        WorkbenchMyUserRankingVo vo = new WorkbenchMyUserRankingVo();       // 最终返回

        commonalityDto.setFieldsName("real_profit");        // 查询字段-总业绩
        List<IndexUserGroupVo> targetProgressList = extendTargetProgressMapper.indexUserGroup(commonalityDto);

        targetProgressList = targetProgressList.stream()
                .sorted(Comparator.comparing(IndexUserGroupVo::getValue).reversed()).collect(Collectors.toList());
        for (int i = 0; i < targetProgressList.size(); i++) {                       // 计算排名
            targetProgressList.get(i).setRanking(i + 1);
        }

        vo.setRankingVoList(targetProgressList.stream().limit(page.getSize()).toList());         // 截取
        vo.setUserRankingVo(targetProgressList.stream().filter(r -> r.getUserId().equals(sysUserVo.getUserId().intValue())).findFirst().orElse(null));    // 获取自己的个人排名

        return vo;
    }

    @Override
    public WorkbenchMyDeptRankingVo workbenchMyDeptRanking(Page<?> page, CommonalityDto commonalityDto, SysUserVo sysUserVo) {
        SysDept sysDept = sysUserVo.getDept();
        if (ObjUtil.isEmpty(commonalityDto.getBeginTime()) || ObjUtil.isEmpty(commonalityDto.getEndTime())) {
            throw new ServiceException("请选择时间范围!");
        }
        if (ObjUtil.isEmpty(commonalityDto.getSelType())) {
            throw new ServiceException("请选择部门类型!");
        }

        // 管理员查看部门层级为部门的级别(如:推广一部),不是管理员查询自己的部门层级
        commonalityDto.setHierarchy(sysUserVo.getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN) ? 4 : sysUserVo.getDept().getHierarchy());
        commonalityDto.setFieldsName("real_profit");        // 查询字段-总业绩

        WorkbenchMyDeptRankingVo vo = new WorkbenchMyDeptRankingVo();       // 最终返回

        List<IndexDeptGroupVo> targetProgressList = extendTargetProgressMapper.indexDeptGroup(commonalityDto);
        targetProgressList = targetProgressList.stream()
                .sorted(Comparator.comparing(IndexDeptGroupVo::getValue).reversed()).collect(Collectors.toList());
        for (int i = 0; i < targetProgressList.size(); i++) {                       // 计算排名
            targetProgressList.get(i).setRanking(i + 1);
        }

        vo.setRankingVoList(targetProgressList.stream().limit(page.getSize()).toList());         // 截取
        vo.setDeptRankingVo(targetProgressList.stream().filter(r -> r.getDeptId().equals(sysDept.getDeptId())).findFirst().orElse(null));    // 获取自己部门排名

        return vo;
    }

    @Override
    public IndexUserGroupAllVo employeeDetailsData(Long id) {
        CommonalityDto commonalityDto = new CommonalityDto();
        commonalityDto.setUserIds(CollUtil.newArrayList(id));

        IndexUserGroupAllVo vo = extendTargetProgressMapper.indexUserAll(commonalityDto);         // 查询每日数据汇总
        IndexUserGroupAllVo result2 = vermicelliMapper.indexUserAll(commonalityDto);              // 查询粉丝登记列表
        vo.setFans2h(result2.getFans2h());          // 值过渡
        vo.setFans5h(result2.getFans5h());
        vo.setFans5k(result2.getFans5k());
        vo.setFans5w(result2.getFans5w());
        vo.setFirstCharge(result2.getFirstCharge());
        return vo;
    }

    @Override
    public IndexDeptGroupAllVo deptDetailsData(Long id) {
        CommonalityDto commonalityDto = new CommonalityDto();
        commonalityDto.setDeptIds(CollUtil.newArrayList(id));

        IndexDeptGroupAllVo vo = extendTargetProgressMapper.indexDeptGroupAll(commonalityDto).get(0);         // 查询每日数据汇总
        IndexDeptGroupAllVo result2 = vermicelliMapper.indexDeptGroupAll(commonalityDto).get(0);              // 查询粉丝登记列表
        vo.setFans2h(result2.getFans2h());          // 值过渡
        vo.setFans5h(result2.getFans5h());
        vo.setFans5k(result2.getFans5k());
        vo.setFans5w(result2.getFans5w());
        vo.setFirstCharge(result2.getFirstCharge());
        return vo;
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public IPage<DetailedVo> selectDataExtendDetailed(Page<?> page, ExtendDetailedDto detailedDto) {
        IPage<DetailedVo> iPage = new Page<>();                                 // 最终返回
        List<DetailedVo> detailedVoList = CollUtil.newArrayList();              // 查询数据汇总
        detailedDto.setSelType(DictConstants.SYS_DEPT_TYPE_EXTEND);             // 查询推广数据

        if (detailedDto.getType() == 5) {                                       // 查个人的
            iPage = extendTargetProgressMapper.detailedUserLimits(page, detailedDto);

            if (CollUtil.isEmpty(iPage.getRecords())) {
                return iPage;
            }
            List<Long> pdUserIds = iPage.getRecords().stream()
                    .filter(u -> StrUtil.isNotEmpty(u.getPdUserIds()))
                    .flatMap(u -> Arrays.stream(u.getPdUserIds().split(","))).map(Long::parseLong).collect(Collectors.toList());
            detailedVoList = extendTargetProgressMapper.ExtendDetailedUserData(
                    detailedDto,
                    pdUserIds.stream().map(String::valueOf).collect(Collectors.joining(",")),
                    iPage.getRecords().stream().map(DetailedVo::getId).toList().stream().map(String::valueOf).collect(Collectors.joining(",")));       // 查数据汇总
            ObjectMapper objectMapper = new ObjectMapper();
            detailedVoList.forEach(d -> {
                if (StrUtil.isNotEmpty(d.getOrderInfo())) {
                    try {
                        // 分解订单字符串
                        Map<String, Object> orderMap = objectMapper.readValue(d.getOrderInfo(), Map.class);
                        d.setChargeUser((Integer) orderMap.get("charge_user"));
                        d.setFortyFiveDays(new BigDecimal(orderMap.get("order_new_money").toString()));
                        d.setRealProfit(new BigDecimal(orderMap.get("order_money").toString()));
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    d.setChargeUser(0);
                    d.setFortyFiveDays(BigDecimal.ZERO);
                    d.setRealProfit(BigDecimal.ZERO);
                }
            });

        } else {                                   // 查部门的
            // 设置查询层级(主管看小组这样推)
            Integer hierarchy = detailedDto.getType() == 4 ? 5 : detailedDto.getType() == 3 ? 4 : detailedDto.getType() == 2 ? 3 : 2;
            detailedDto.setHierarchy(hierarchy);
            iPage = extendTargetProgressMapper.ExtendDetailedDeptLimits(page, detailedDto);       // 查数据汇总

            if (CollUtil.isEmpty(iPage.getRecords())) {
                return iPage;
            }
            detailedVoList = extendTargetProgressMapper.ExtendDetailedDeptData(
                    detailedDto,
                    iPage.getRecords().stream().map(DetailedVo::getId).collect(Collectors.toList()).stream().map(String::valueOf).collect(Collectors.joining(",")));       // 查数据汇总
            ObjectMapper objectMapper = new ObjectMapper();
            detailedVoList.forEach(d -> {
                try {
                    // 分解粉丝登记字符串
                    if (StrUtil.isNotEmpty(d.getFansInfo())) {
                        Map<String, Integer> fansMap = objectMapper.readValue(d.getFansInfo(), Map.class);
                        d.setFans2h(fansMap.get("fans2h"));
                        d.setFans5h(fansMap.get("fans5h"));
                        d.setFans5k(fansMap.get("fans5k"));
                        d.setFans5kNew(fansMap.get("fans5kNew"));
                        d.setFans5w(fansMap.get("fans5w"));
                        d.setFirstCharge(fansMap.get("firstCharge").longValue());
                    } else {
                        d.setFans2h(0);
                        d.setFans5h(0);
                        d.setFans5k(0);
                        d.setFans5kNew(0);
                        d.setFans5w(0);
                        d.setFirstCharge(0L);
                    }
                    // 分解订单字符串
                    if (StrUtil.isNotEmpty(d.getOrderInfo())) {
                        Map<String, Object> orderMap = objectMapper.readValue(d.getOrderInfo(), Map.class);
                        d.setChargeUser((Integer) orderMap.get("charge_user"));
                        d.setFortyFiveDays(new BigDecimal(orderMap.get("order_new_money").toString()));
                        d.setRealProfit(new BigDecimal(orderMap.get("order_money").toString()));
                    } else {
                        d.setChargeUser(0);
                        d.setFortyFiveDays(BigDecimal.ZERO);
                        d.setRealProfit(BigDecimal.ZERO);
                    }
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
        }

        for (DetailedVo p : iPage.getRecords()) {
            DetailedVo vo = detailedVoList.stream().filter(e -> e.getId().equals(p.getId())).findFirst().orElse(null);

            if (ObjUtil.isNotNull(vo)) {
                p.setFans2h(vo.getFans2h());
                p.setFans5h(vo.getFans5h());
                p.setFans5k(vo.getFans5k());
                p.setFans5kNew(vo.getFans5kNew());
                p.setFans5w(vo.getFans5w());
                p.setFirstCharge(vo.getFirstCharge());
                p.setChargeUser(vo.getChargeUser());
                p.setRealProfit(vo.getRealProfit());
                p.setFortyFiveDays(vo.getFortyFiveDays());
                p.setFriend(vo.getFriend());
                p.setRegister(vo.getRegister());
                p.setQualityUsers(vo.getQualityUsers());
                p.setReceive(vo.getReceive());
                p.setReceiveVip(vo.getReceiveVip());
            }
        }

        for (DetailedVo v : iPage.getRecords()) {
            // 拆分出部门-团队-小组
            List<String> stringList = Arrays.stream(v.getAncestorsNames().split("-")).collect(Collectors.toList());      // 拆分部门名
            int size = stringList.size() - 2;       // 要截取的
            if (size == 1) {
                v.setTeamName1(stringList.get(stringList.size() - 1));
            } else if (size == 2) {
                v.setTeamName1(stringList.get(stringList.size() - 2));
                v.setTeamName2(stringList.get(stringList.size() - 1));
            } else if (size == 3) {
                v.setTeamName1(stringList.get(stringList.size() - 3));
                v.setTeamName2(stringList.get(stringList.size() - 2));
                v.setTeamName3(stringList.get(stringList.size() - 1));
            }
        }

        return iPage;
    }

    @Override
    public List<EmployeeMonthRankingVo> employeeMonthRanking(CommonalityDto dto) {
        if (ObjUtil.isNull(dto.getUserId())) {
            throw new ServiceException("参数有误!");
        }

        List<EmployeeMonthRankingVo> groupVoList = new ArrayList<>();        // 最终返回

        /**crm的数据排名(好友、注册数)*/
        Map<String, List<FriendRegisterChargeVo>> map = remoteFriendService.getFriendRegisterRanking(CollUtil.newArrayList(),
                LocalDateTimeUtil.of(dto.getBeginTime()).toString(),
                LocalDateTimeUtil.of(dto.getEndTime()).toString(),
                SecurityConstants.INNER).getData();

        /**
         * 总业绩、一交、二交、新增、优质
         */
        List<IndexUserGroupAllVo> rankingList = extendTargetProgressMapper.selPeopleRanking(dto);

        /**粉丝登记列表*/
        List<IndexUserGroupAllVo> vermicelliList = vermicelliMapper.selPeopleRanking(dto);

        // 初始化对象
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("好友").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("注册").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("首充").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("总业绩").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("一交").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("二交").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("新增").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("优质").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("200粉").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("5k粉").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());
        groupVoList.add(EmployeeMonthRankingVo.builder().fieldsName("5w粉").ranking(0).rankingPeople(0).value(BigDecimal.ZERO).build());

        // 查找自己的排名和排名人数
        if (ObjUtil.isNotNull(map)) {
            // 好友、注册数
            List<FriendRegisterChargeVo> friendList = map.get("好友排名");
            if (CollUtil.isNotEmpty(friendList)) {
                groupVoList.forEach(g -> {
                    if (g.getFieldsName().equals("好友")) {
                        g.setRankingPeople(friendList.size());
                        FriendRegisterChargeVo vo = friendList.stream().filter(f -> f.getId().equals(dto.getUserId())).findFirst().orElse(null);
                        if (ObjUtil.isNotNull(vo)) {
                            g.setRanking(vo.getRanking());
                            g.setValue(BigDecimal.valueOf(vo.getFriend()));
                        }
                    }
                });
            }
            List<FriendRegisterChargeVo> registerList = map.get("注册排名");
            if (CollUtil.isNotEmpty(registerList)) {
                groupVoList.forEach(g -> {
                    if (g.getFieldsName().equals("注册")) {
                        g.setRankingPeople(registerList.size());
                        FriendRegisterChargeVo vo = registerList.stream().filter(f -> f.getId().equals(dto.getUserId())).findFirst().orElse(null);
                        if (ObjUtil.isNotNull(vo)) {
                            g.setRanking(vo.getRanking());
                            g.setValue(BigDecimal.valueOf(vo.getRegister()));
                        }
                    }
                });
            }
        }

        if (CollUtil.isNotEmpty(rankingList)) {
            // 总业绩、一交、二交、新增、优质
            List<IndexUserGroupAllVo> ranking1 = rankingList.stream()       // 总业绩排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getRealProfit).reversed()).collect(Collectors.toList());
            List<IndexUserGroupAllVo> ranking2 = rankingList.stream()       // 一交排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getRealProfit).reversed()).collect(Collectors.toList());
            List<IndexUserGroupAllVo> ranking3 = rankingList.stream()       // 二交排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getRealProfit).reversed()).collect(Collectors.toList());
            List<IndexUserGroupAllVo> ranking4 = rankingList.stream()       // 新增排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getRealProfit).reversed()).collect(Collectors.toList());
            List<IndexUserGroupAllVo> ranking5 = rankingList.stream()       // 优质排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getRealProfit).reversed()).collect(Collectors.toList());
            groupVoList.forEach(g -> {
                if (g.getFieldsName().equals("总业绩")) {
                    g.setRankingPeople(ranking1.size());
                    for (int i = 0; i < ranking1.size(); i++) {
                        if (ranking1.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(ranking1.get(i).getRealProfit());
                        }
                    }
                } else if (g.getFieldsName().equals("一交")) {
                    g.setRankingPeople(ranking2.size());
                    for (int i = 0; i < ranking2.size(); i++) {
                        if (ranking2.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(BigDecimal.valueOf(ranking2.get(i).getReceive()));
                        }
                    }
                } else if (g.getFieldsName().equals("二交")) {
                    g.setRankingPeople(ranking3.size());
                    for (int i = 0; i < ranking3.size(); i++) {
                        if (ranking3.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(BigDecimal.valueOf(ranking3.get(i).getReceiveVip()));
                        }
                    }
                } else if (g.getFieldsName().equals("新增")) {
                    g.setRankingPeople(ranking4.size());
                    for (int i = 0; i < ranking4.size(); i++) {
                        if (ranking4.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(ranking4.get(i).getFortyFiveDays());
                        }
                    }
                } else if (g.getFieldsName().equals("优质")) {
                    g.setRankingPeople(ranking5.size());
                    for (int i = 0; i < ranking5.size(); i++) {
                        if (ranking5.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(BigDecimal.valueOf(ranking5.get(i).getQualityUsers()));
                        }
                    }
                }
            });
        }

        if (CollUtil.isNotEmpty(vermicelliList)) {
            // 粉丝登记列表
            List<IndexUserGroupAllVo> vermicelliListTwoHundred = rankingList.stream()       // 200粉排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getFans2h).reversed()).collect(Collectors.toList());
            List<IndexUserGroupAllVo> vermicelliListFiveThousand = rankingList.stream()     // 5k粉排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getFans5k).reversed()).collect(Collectors.toList());
            List<IndexUserGroupAllVo> vermicelliListFiftyThousand = rankingList.stream()    // 5w粉排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getFans5w).reversed()).collect(Collectors.toList());
            List<IndexUserGroupAllVo> vermicelliListFirstCharge = rankingList.stream()      // 首充粉排名
                    .sorted(Comparator.comparing(IndexUserGroupAllVo::getFirstCharge).reversed()).collect(Collectors.toList());
            groupVoList.forEach(g -> {
                if (g.getFieldsName().equals("200")) {
                    g.setRankingPeople(vermicelliListTwoHundred.size());
                    for (int i = 0; i < vermicelliListTwoHundred.size(); i++) {
                        if (vermicelliListTwoHundred.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(BigDecimal.valueOf(vermicelliListFiveThousand.get(i).getFans2h()));
                        }
                    }
                } else if (g.getFieldsName().equals("5k粉")) {
                    g.setRankingPeople(vermicelliListFiveThousand.size());
                    for (int i = 0; i < vermicelliListFiveThousand.size(); i++) {
                        if (vermicelliListFiveThousand.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(BigDecimal.valueOf(vermicelliListFiveThousand.get(i).getFans5k()));
                        }
                    }
                } else if (g.getFieldsName().equals("5w粉")) {
                    g.setRankingPeople(vermicelliListFiftyThousand.size());
                    for (int i = 0; i < vermicelliListFiftyThousand.size(); i++) {
                        if (vermicelliListFiftyThousand.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(BigDecimal.valueOf(vermicelliListFiftyThousand.get(i).getFans5w()));
                        }
                    }
                } else if (g.getFieldsName().equals("首充粉")) {
                    g.setRankingPeople(vermicelliListFirstCharge.size());
                    for (int i = 0; i < vermicelliListFirstCharge.size(); i++) {
                        if (vermicelliListFirstCharge.get(i).getUserId().equals(dto.getUserId())) {
                            g.setRanking(i + 1);
                            g.setValue(BigDecimal.valueOf(vermicelliListFirstCharge.get(i).getFirstCharge()));
                        }
                    }
                }
            });
        }

        return groupVoList;
    }

    @Override
    public int insertAllTargetProgress(List<ExtendTargetProgress> list) {
        return extendTargetProgressMapper.insertAll(list);
    }

    @Override
    public int updateAllTargetProgress(List<ExtendTargetProgress> list) {
        return extendTargetProgressMapper.updateAll(list);
    }

    @Override
    public int insertAllTargetProgressHour(List<ExtendTargetProgressHour> list) {
        return targetHourMapper.insertAll(list);
    }

    @Override
    public int deleteTargetProgressHour() {
        return targetHourMapper.deleteHour(DateUtil.dateConvertString(DateUtil.getAfterDays(new Date(), -1), DateUtil.y_M_d));
    }

    @Override
    public ExtendTargetProgress finallyOne() {
        return extendTargetProgressMapper.selectOne(new LambdaQueryWrapper<ExtendTargetProgress>()
                .orderByDesc(ExtendTargetProgress::getDate)
                .orderByDesc(ExtendTargetProgress::getId)
                .last("limit 1"));
    }

//    @Override
//    public Page<GroupDeptVo> getGroupDataPerformance(Page page, CommonDataStatisticsQuery commonDataStatisticsQuery) {
//        // 获取部门/团队/小组基础数据
//        Page<GroupDeptVo> groupDeptvoPage = Optional.ofNullable(remoteDeptService.getGroupDataList(QueryPage
//                .builder()
//                // 部门级别 （1集团 2公司 3部门 4团队 5小组）
//                .deptQuery(DeptQuery.builder().deptLevel(commonDataStatisticsQuery.getType()).build())
//                .page(page)
//                .build(),
//                SecurityConstants.INNER)
//                .getData())
//                .orElseThrow(() -> new ServiceException("未查询到数据"));
//        List<GroupDeptVo> groupDeptVoList = groupDeptvoPage.getRecords();
//
//        List<GroupDeptVo> result = getDataSummary(groupDeptVoList);
//        page.setRecords(result);
//        page.setTotal(groupDeptvoPage.getTotal());
//        return page;
//    }
//
//    private List<GroupDeptVo> getDataSummary(List<GroupDeptVo> groupDeptVoList) {
//        List<GroupDeptVo> result = groupDeptVoList.stream().peek(groupDeptVo -> {
//            // 总业绩
//            totalPerformance(groupDeptVo);
//            // 新增业绩
//            newAchievements(groupDeptVo);
//            // 好友
//            getFriend(groupDeptVo);
//            // 注册
//            getRegister(groupDeptVo);
//            // 二交
//            getSecondaryHandover(groupDeptVo);
//            // 200粉
//            // 5k粉
//            // 5w粉
//        }).toList();
//        return result;
//    }
//
//    // 二交
//    private void getSecondaryHandover(GroupDeptVo groupDeptVo) {
//
//    }
//
//    // 注册
//    private void getRegister(GroupDeptVo groupDeptVo) {
//    }
//
//    // 好友
//    private void getFriend(GroupDeptVo groupDeptVo) {
//
//    }
//
//    // 新增业绩
//    private void newAchievements(GroupDeptVo groupDeptVo) {
//    }
//
//    // 总业绩
//    private void totalPerformance(GroupDeptVo groupDeptVo) {
//        // 获取小组年计划
//        BigDecimal yearPlan = goalExtendPackMapper.getYearPlan(groupDeptVo.getDeptId());
//        groupDeptVo.setYearPlan(yearPlan);
//        // 获取小组月计划
//        BigDecimal monthPlan = goalExtendPackMapper.getMonthPlan(groupDeptVo.getDeptId());
//        groupDeptVo.setMonthPlan(monthPlan);
//        // 获取业绩统计信息
//        RechargeCollectVo recharge = goalExtendPackMapper.getRechargeCollect(groupDeptVo.getDeptId());
//        // 获取本月总业绩
//        groupDeptVo.setMonthRecharge(recharge.getMonthRecharge());
//        // 获取上月总业绩
//        groupDeptVo.setLastMonthRecharge(recharge.getLastMonthRecharge());
//        // 获取今年总业绩
//        groupDeptVo.setYearRecharge(recharge.getYearRecharge());
//        // 获取去年总业绩
//        groupDeptVo.setLastYearRecharge(recharge.getLastYearRecharge());
//        // 获取本月平均业绩
//        groupDeptVo.setMonthAvgRecharge(recharge.getMonthAvgRecharge());
//        // 获取今年平均业绩
//        groupDeptVo.setYearAvgRecharge(recharge.getYearAvgRecharge());
//    }

//    @Override
//    public List<OperationalBusinessDataVo> getOperationalBusinessData(Page page, OperateBusinessQuery query) {
//        List<OperationalBusinessDataVo> result = new ArrayList<>();
//
//        // 获取所有运营人员
//        Page<SysUserVo> data = remoteUserService.getPageUserList(
//                QueryPage.builder()
//                        .page(page)
//                        .userQuery(UserQuery
//                                .builder()
//                                .deptId(query.getDeptId())
//                                .deptType(2)
//                                .pdType("2")
//                                .pdUserIdNotNull(1)
//                                .orName(query.getName())
//                                .status(query.getStatus())
//                                .build())
//                        .build(),
//                SecurityConstants.INNER
//        ).getData();
//
//        if (ObjUtil.isNull(data)) {
//            return Collections.emptyList();
//        }
//
//        page.setTotal(data.getTotal());
//        List<SysUserVo> sysUserVos = data.getRecords();
//
//        if (CollUtil.isEmpty(sysUserVos)) {
//            return Collections.emptyList();
//        }
//
//        for (SysUserVo userVo : sysUserVos) {
//            OperationalBusinessDataVo businessDataVo = new OperationalBusinessDataVo();
//            Long userId = userVo.getUserId();
//
//            // 设置运营人员信息
//            businessDataVo.setOperateName(StrUtil.format("{}&{}", userVo.getNickName(), userVo.getUserName()));
//            businessDataVo.setUserId(userId);
//
//            // 获取主播开播状态信息
//            AnchorLiveStatusCollectVo anchorStatus = extendTargetProgressMapper.getAnchorUserStatus(userId);
//            if (anchorStatus != null) {
//                businessDataVo.setBeginToShowNum(anchorStatus.getSumBeginToShow());
//                businessDataVo.setDiscontinueBroadcastingNum(anchorStatus.getSumCeaseBroadcasting());
//                businessDataVo.setOffAirBroadcastNum(anchorStatus.getSumOffAirBroadcast());
//            }
//
//            // 获取打赏与交接数据
//            HandoverDataVo handoverData = extendTargetProgressMapper.getHandoverData(userId,query);
//            Long rewardNum = extendTargetProgressMapper.getRewardNum(userId,query);
//            BigDecimal rewardAmount = extendTargetProgressMapper.getRewardAmount(userId,query);
//            // 交接人数
//            Long handoverNum = handoverData.getHandoverNum();
//            // 交接打赏人数
//            Long handoverRewardNum = handoverData.getHandoverRewardNum();
//            // 首充人数
//            Long firstChargeNum = handoverData.getFirstChargeNum();
//
//
//            // 接粉打赏率
//            BigDecimal joinRewardRate = (handoverRewardNum != null && handoverRewardNum != 0 && handoverNum != null && handoverNum != 0)
//                    ? NumberUtil.div(new BigDecimal(handoverRewardNum),new BigDecimal(handoverNum),2,RoundingMode.HALF_UP)
//                    : BigDecimal.ZERO;
//
//
//            // 接粉首冲率
//            BigDecimal handoverFirstChargeRate = (firstChargeNum != null && firstChargeNum != 0 && handoverNum != null && handoverNum != 0)
//                    ? NumberUtil.div(new BigDecimal(firstChargeNum),new BigDecimal(handoverNum),2,RoundingMode.HALF_UP)
//                    : BigDecimal.ZERO;
//
//            businessDataVo.setHandoverNum(handoverNum);
//            businessDataVo.setHandoverRewardNum(handoverRewardNum);
//            businessDataVo.setTotalRewardNum(rewardNum);
//            businessDataVo.setHandoverRewardRate(joinRewardRate);
//            businessDataVo.setFirstChargeNum(firstChargeNum);
//            businessDataVo.setHandoverFirstChargeRate(handoverFirstChargeRate);
//            businessDataVo.setRewardAmount(rewardAmount);
//
//            // 获取粉丝数据
//            OperateFansVo fansVo = extendTargetProgressMapper.getFansAchievedCollect(userId,query);
//            if (fansVo != null) {
//                businessDataVo.setFans2h(fansVo.getFans2h());
//                businessDataVo.setFans5k(fansVo.getFans5k());
//                businessDataVo.setFans5w(fansVo.getFans5w());
//                businessDataVo.setFans10w(fansVo.getFans10w());
//            }
//
//            result.add(businessDataVo);
//        }
//
//        return result;
//    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public IPage<OperationalBusinessDataVo> getOperationalBusinessData(Page page, OperateBusinessQuery detailedDto) {
        IPage<OperationalBusinessDataVo> iPage = extendTargetProgressMapper.operateDetailedUserLimits(page, detailedDto);
        if (CollUtil.isEmpty(iPage.getRecords())) {
            return iPage;
        }
        List<Long> pdUserIds = iPage.getRecords().stream()
                .filter(u -> StrUtil.isNotEmpty(u.getPdUserIds()))
                .flatMap(u -> Arrays.stream(u.getPdUserIds().split(","))).map(Long::parseLong).toList();

        List<OperateDetailVo> operateDetailVos = extendTargetProgressMapper.OperateDetailedUserData(
                detailedDto,
                pdUserIds.stream().map(String::valueOf).collect(Collectors.joining(",")),
                iPage.getRecords().stream().map(OperationalBusinessDataVo::getId).toList().stream().map(String::valueOf).collect(Collectors.joining(",")));
        for (OperationalBusinessDataVo record : iPage.getRecords()) {
            Map<Long, OperateDetailVo> map = operateDetailVos.stream().collect(Collectors.toMap(OperateDetailVo::getId, Function.identity()));
            OperateDetailVo operateDetailVo = map.get(record.getId());
            if (operateDetailVo != null) {
                // 交接人数
                Long handoverNum = operateDetailVo.getHandoverNum();
                // 交接打赏人数
                Long handoverRewardNum = operateDetailVo.getHandoverRewardNum();
                // 首充人数
                Long firstChargeNum = operateDetailVo.getFirstChargeNum();

                // 接粉打赏率
                BigDecimal handoverRewardRate = (handoverRewardNum != null && handoverRewardNum != 0 && handoverNum != null && handoverNum != 0)
                        ? NumberUtil.div(new BigDecimal(handoverRewardNum), new BigDecimal(handoverNum), 6, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100))
                        .setScale(4, RoundingMode.HALF_UP)
                        : BigDecimal.ZERO;
                // 接粉首冲率
                BigDecimal handoverFirstChargeRate = (firstChargeNum != null && firstChargeNum != 0 && handoverNum != null && handoverNum != 0)
                        ? NumberUtil.div(new BigDecimal(firstChargeNum), new BigDecimal(handoverNum), 6, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100))
                        .setScale(4, RoundingMode.HALF_UP)
                        : BigDecimal.ZERO;
                // 2h粉
                record.setFans2h(operateDetailVo.getFans2h());
                // 5K粉
                record.setFans5k(operateDetailVo.getFans5k());
                // 10W粉
                record.setFans10w(operateDetailVo.getFans10w());
                // 交接
                record.setHandoverNum(operateDetailVo.getHandoverNum());
                // 首充
                record.setFirstChargeNum(operateDetailVo.getFirstChargeNum());
                // 打赏业绩
                record.setRewardAmount(operateDetailVo.getRewardAmount());
                // 开播数
                record.setBeginToShowNum(operateDetailVo.getSumBeginToShow());
                // 休播数
                record.setOffAirBroadcastNum(operateDetailVo.getSumOffAirBroadcast());
                // 待停播
                record.setToBeDiscontinued(operateDetailVo.getToBeDiscontinued());
                // 停播数
                record.setDiscontinueBroadcastingNum(operateDetailVo.getSumCeaseBroadcasting());
                // 总打赏数
                record.setTotalRewardNum(operateDetailVo.getRewardNum());
                // 接粉打赏率
                record.setHandoverRewardRate(handoverRewardRate);
                // 接粉首充率
                record.setHandoverFirstChargeRate(handoverFirstChargeRate);
                // 接粉数
                record.setHandoverRewardNum(handoverRewardNum);
            }
        }

        return iPage;
    }

}
