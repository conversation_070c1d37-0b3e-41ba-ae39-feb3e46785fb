package com.yooa.extend.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.extend.api.domain.GoalExtendPack;
import com.yooa.extend.api.domain.dto.GoalExtendPackQueryDto;

import java.util.List;


/**
 * 目标打包 - 服务层
 */
public interface GoalExtendPackService extends IService<GoalExtendPack> {

    /**
     * 获取目标打包列表
     */
    List<GoalExtendPack> getGoalExtendPackList(Page<GoalExtendPack> page, GoalExtendPackQueryDto query);

}
