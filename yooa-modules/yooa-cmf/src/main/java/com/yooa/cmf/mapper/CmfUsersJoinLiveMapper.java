package com.yooa.cmf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.cmf.api.domain.CmfUsersJoinLive;
import com.yooa.cmf.api.domain.vo.ConvertCustomerJoinAnchorVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户主播交接 - 数据层
 */
public interface CmfUsersJoinLiveMapper extends BaseMapper<CmfUsersJoinLive> {

    List<ConvertCustomerJoinAnchorVo> selectListByGtId(@Param("id") Long id);

    List<ConvertCustomerJoinAnchorVo> selectListByStatusChange(@Param("ids") List<Long> ids);
}




