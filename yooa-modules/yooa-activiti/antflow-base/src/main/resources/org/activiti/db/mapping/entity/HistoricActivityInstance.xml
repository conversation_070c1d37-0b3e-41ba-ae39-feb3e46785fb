<?xml version="1.0" encoding="UTF-8" ?>

<!--
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity">

  <!-- HISTORIC ACTIVITY INSTANCE INSERT -->
  
  <insert id="insertHistoricActivityInstance" parameterType="org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity">
      insert into ${prefix}ACT_HI_ACTINST (
        ID_,
        PROC_DEF_ID_,
        PROC_INST_ID_,
        EXECUTION_ID_,
        ACT_ID_,
        TASK_ID_,
        CALL_PROC_INST_ID_,
        ACT_NAME_,
        ACT_TYPE_,
        ASSIGNEE_,
        START_TIME_,
        END_TIME_,
        DURATION_,
        TENANT_ID_
      ) values (
        #{id ,jdbcType=VARCHAR},
        #{processDefinitionId, jdbcType=VARCHAR},
        #{processInstanceId, jdbcType=VARCHAR},
        #{executionId, jdbcType=VARCHAR},
        #{activityId ,jdbcType=VARCHAR},
        #{taskId ,jdbcType=VARCHAR},
        #{calledProcessInstanceId ,jdbcType=VARCHAR},
        #{activityName ,jdbcType=VARCHAR},
        #{activityType ,jdbcType=VARCHAR},
        #{assignee ,jdbcType=VARCHAR},
        #{startTime, jdbcType=TIMESTAMP},
        #{endTime, jdbcType=TIMESTAMP},
        #{durationInMillis ,jdbcType=BIGINT},
        #{tenantId, jdbcType=VARCHAR}
      )
  </insert>
  
  <insert id="bulkInsertHistoricActivityInstance" parameterType="java.util.List">
      insert into ${prefix}ACT_HI_ACTINST (
        ID_,
        PROC_DEF_ID_,
        PROC_INST_ID_,
        EXECUTION_ID_,
        ACT_ID_,
        TASK_ID_,
        CALL_PROC_INST_ID_,
        ACT_NAME_,
        ACT_TYPE_,
        ASSIGNEE_,
        START_TIME_,
        END_TIME_,
        DURATION_,
        TENANT_ID_
      ) values 
      <foreach collection="list" item="historicActivityInstance" index="index" separator=",">
         (#{historicActivityInstance.id ,jdbcType=VARCHAR},
          #{historicActivityInstance.processDefinitionId, jdbcType=VARCHAR},
          #{historicActivityInstance.processInstanceId, jdbcType=VARCHAR},
          #{historicActivityInstance.executionId, jdbcType=VARCHAR},
          #{historicActivityInstance.activityId ,jdbcType=VARCHAR},
          #{historicActivityInstance.taskId ,jdbcType=VARCHAR},
          #{historicActivityInstance.calledProcessInstanceId ,jdbcType=VARCHAR},
          #{historicActivityInstance.activityName ,jdbcType=VARCHAR},
          #{historicActivityInstance.activityType ,jdbcType=VARCHAR},
          #{historicActivityInstance.assignee ,jdbcType=VARCHAR},
          #{historicActivityInstance.startTime, jdbcType=TIMESTAMP},
          #{historicActivityInstance.endTime, jdbcType=TIMESTAMP},
          #{historicActivityInstance.durationInMillis ,jdbcType=BIGINT},
          #{historicActivityInstance.tenantId, jdbcType=VARCHAR})
      </foreach>
  </insert>
  
  <insert id="bulkInsertHistoricActivityInstance_oracle" parameterType="java.util.List">
      INSERT ALL 
      <foreach collection="list" item="historicActivityInstance" index="index">
        INTO ${prefix}ACT_HI_ACTINST (
          ID_,
          PROC_DEF_ID_,
          PROC_INST_ID_,
          EXECUTION_ID_,
          ACT_ID_,
          TASK_ID_,
          CALL_PROC_INST_ID_,
          ACT_NAME_,
          ACT_TYPE_,
          ASSIGNEE_,
          START_TIME_,
          END_TIME_,
          DURATION_,
          TENANT_ID_
        ) VALUES 
           (#{historicActivityInstance.id ,jdbcType=VARCHAR},
            #{historicActivityInstance.processDefinitionId, jdbcType=VARCHAR},
            #{historicActivityInstance.processInstanceId, jdbcType=VARCHAR},
            #{historicActivityInstance.executionId, jdbcType=VARCHAR},
            #{historicActivityInstance.activityId ,jdbcType=VARCHAR},
            #{historicActivityInstance.taskId ,jdbcType=VARCHAR},
            #{historicActivityInstance.calledProcessInstanceId ,jdbcType=VARCHAR},
            #{historicActivityInstance.activityName ,jdbcType=VARCHAR},
            #{historicActivityInstance.activityType ,jdbcType=VARCHAR},
            #{historicActivityInstance.assignee ,jdbcType=VARCHAR},
            #{historicActivityInstance.startTime, jdbcType=TIMESTAMP},
            #{historicActivityInstance.endTime, jdbcType=TIMESTAMP},
            #{historicActivityInstance.durationInMillis ,jdbcType=BIGINT},
            #{historicActivityInstance.tenantId, jdbcType=VARCHAR})
      </foreach>
    SELECT * FROM dual
  </insert>

  <!-- HISTORIC ACTIVITY INSTANCE UPDATE -->
  
  <update id="updateHistoricActivityInstance" parameterType="org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity">
    update ${prefix}ACT_HI_ACTINST set
      EXECUTION_ID_ = #{executionId, jdbcType=VARCHAR},
      ASSIGNEE_ = #{assignee, jdbcType=VARCHAR},
      END_TIME_ = #{endTime, jdbcType=TIMESTAMP},
      DURATION_ = #{durationInMillis ,jdbcType=BIGINT}
    where ID_ = #{id}
  </update>

  <!-- HISTORIC ACTIVITY INSTANCE DELETE -->
  
  <delete id="deleteHistoricActivityInstancesByProcessInstanceId">
    delete from ${prefix}ACT_HI_ACTINST where PROC_INST_ID_ = #{processInstanceId}
  </delete>
  
  <!-- HISTORIC ACTIVITY INSTANCE RESULT MAP -->

  <resultMap id="historicActivityInstanceResultMap" type="org.activiti.engine.impl.persistence.entity.HistoricActivityInstanceEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="processDefinitionId" column="PROC_DEF_ID_" jdbcType="VARCHAR" />
    <result property="processInstanceId" column="PROC_INST_ID_" jdbcType="VARCHAR" />
    <result property="executionId" column="EXECUTION_ID_" jdbcType="VARCHAR" />
    <result property="activityId" column="ACT_ID_" jdbcType="VARCHAR" />
    <result property="taskId" column="TASK_ID_" jdbcType="VARCHAR" />
    <result property="calledProcessInstanceId" column="CALL_PROC_INST_ID_" jdbcType="VARCHAR" />
    <result property="activityName" column="ACT_NAME_" jdbcType="VARCHAR" />
    <result property="activityType" column="ACT_TYPE_" jdbcType="VARCHAR" />
    <result property="assignee" column="ASSIGNEE_" jdbcType="VARCHAR" />
    <result property="startTime" column="START_TIME_" jdbcType="TIMESTAMP" />
    <result property="endTime" column="END_TIME_" jdbcType="TIMESTAMP" />
    <result property="durationInMillis" column="DURATION_" jdbcType="BIGINT" />
    <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR" />
  </resultMap>

  <!-- HISTORIC ACTIVITY INSTANCE SELECT -->
  
  <select id="selectHistoricActivityInstance" resultMap="historicActivityInstanceResultMap">
    select * from ${prefix}ACT_HI_ACTINST where ACT_ID_ = #{activityId} and PROC_INST_ID_ = #{processInstanceId}
  </select>

  <select id="selectHistoricActivityInstancesByQueryCriteria" parameterType="org.activiti.engine.impl.HistoricActivityInstanceQueryImpl" resultMap="historicActivityInstanceResultMap">
    ${limitBefore}
    select RES.* ${limitBetween}
    <include refid="selectHistoricActivityInstancesByQueryCriteriaSql"/>
    ${orderBy}
    ${limitAfter}
  </select>
  
  <select id="selectHistoricActivityInstanceCountByQueryCriteria" parameterType="org.activiti.engine.impl.HistoricActivityInstanceQueryImpl" resultType="long">
    select count(RES.ID_)
    <include refid="selectHistoricActivityInstancesByQueryCriteriaSql"/>
  </select>
  
  <sql id="selectHistoricActivityInstancesByQueryCriteriaSql">
    from ${prefix}ACT_HI_ACTINST RES
    <where>
      <if test="processInstanceId != null">
        RES.PROC_INST_ID_ = #{processInstanceId}
      </if>
      <if test="activityInstanceId != null">
        and RES.ID_ = #{activityInstanceId}
      </if>
      <if test="executionId != null">
        and RES.EXECUTION_ID_ = #{executionId}
      </if>
      <if test="processDefinitionId != null">
        and RES.PROC_DEF_ID_ = #{processDefinitionId}
      </if>
      <if test="activityId != null">
        and RES.ACT_ID_ = #{activityId}
      </if>
      <if test="activityName != null">
        and RES.ACT_NAME_ = #{activityName}
      </if>
      <if test="activityType != null">
        and RES.ACT_TYPE_ = #{activityType}
      </if>
      <if test="assignee != null">
        and RES.ASSIGNEE_ = #{assignee}
      </if>
      <if test="tenantId != null">
        and RES.TENANT_ID_ = #{tenantId}
      </if>
      <if test="tenantIdLike != null">
        and RES.TENANT_ID_ like #{tenantIdLike}${wildcardEscapeClause}
      </if>
      <if test="withoutTenantId">
        and (RES.TENANT_ID_ = '' or RES.TENANT_ID_ is null)
      </if>
      <if test="unfinished">
        and RES.END_TIME_ is null
      </if>
      <if test="finished">
       and RES.END_TIME_ is not null
      </if>
    </where>
  </sql>
  
  <select id="selectHistoricActivityInstanceByNativeQuery" parameterType="java.util.Map" resultMap="historicActivityInstanceResultMap">
  	<if test="resultType == 'LIST_PAGE'">
    	${limitBefore}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
    	${limitAfter}
    </if>
  </select>
  
  <select id="selectHistoricActivityInstanceByNativeQuery_mssql_or_db2" parameterType="java.util.Map" resultMap="historicActivityInstanceResultMap">
  	<if test="resultType == 'LIST_PAGE'">
        ${limitBeforeNativeQuery}
  	</if>
  	${sql} 
    <if test="resultType == 'LIST_PAGE'">
  		${limitAfter}
  	</if>
  </select>

  <select id="selectHistoricActivityInstanceCountByNativeQuery" parameterType="java.util.Map" resultType="long">
    ${sql}
  </select>
</mapper>
