<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.openoa.engine.bpmnconf.mapper.BpmVariableMapper">


    <select id="getElementIdsdByNodeId" resultType="java.lang.String">
        select tbvs.element_id from t_bpm_variable tbv join t_bpm_variable_single tbvs  on tbv.id=tbvs.variable_id
        where tbv.process_num=#{processNum} and tbvs.node_id=#{nodeId}
        union all
        select tbvm.element_id from t_bpm_variable tbv join t_bpm_variable_multiplayer tbvm on tbvm.variable_id=tbv.id
        where tbv.process_num=#{processNum} and tbvm.node_id=#{nodeId}
    </select>
    <select id="getNodeIdsByeElementId" resultType="java.lang.String">
        select tbvs.node_id from t_bpm_variable tbv join t_bpm_variable_single tbvs  on tbv.id=tbvs.variable_id
        where tbv.process_num=#{processNum} and tbvs.element_id=#{elementId}
        union all
        select tbvm.node_id from t_bpm_variable tbv join t_bpm_variable_multiplayer tbvm on tbvm.variable_id=tbv.id
        where tbv.process_num=#{processNum} and tbvm.element_id=#{elementId}
    </select>

</mapper>
