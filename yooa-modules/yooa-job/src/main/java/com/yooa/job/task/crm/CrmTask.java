package com.yooa.job.task.crm;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.crm.api.RemoteCustomerViewLiveTimeService;
import com.yooa.crm.api.RemoteFriendService;
import com.yooa.job.mapper.JobMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 *
 */
@AllArgsConstructor
@Component("crmTask")
public class CrmTask {

    private final RemoteFriendService remoteQualityService;
    private final RemoteCustomerViewLiveTimeService remoteCustomerViewLiveTimeService;
    private final JobMapper jobMapper;

    /**
     * 扫描好友的修改时间是否大于15/30天为更新,由领取变为空闲
     * 好友
     * 每天执行一次
     */
    public void ProbationPeriod() {
        remoteQualityService.ProbationPeriod(SecurityConstants.INNER);
    }

    /**
     * 刷cf的优质用户达成时间
     */
    public void probationPeriod() {
        remoteCustomerViewLiveTimeService.refreshQuality(SecurityConstants.INNER);
    }

    /**
     * 刷订单中有推广pyuserid的但没有userid
     */
    public void updateOrderExtendId() {
        jobMapper.updateOrderExtendId();
    }

    /**
     * 刷订单中有VIPpyuserid的但没有userid
     */
    public void updateOrderServeId() {
        jobMapper.updateOrderServeId();
    }
}
