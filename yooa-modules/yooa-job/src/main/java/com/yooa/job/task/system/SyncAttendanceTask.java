package com.yooa.job.task.system;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.system.api.RemoteSyncAttendanceService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component("syncAttendanceTask")
public class SyncAttendanceTask {

    private final RemoteSyncAttendanceService remoteSyncAttendanceService;

    /**
     * 同步考勤组数据
     */
    public void syncAttendGroup() {

        R r = remoteSyncAttendanceService.syncAttendGroup(SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            throw new ServiceException("同步考勤组数据定时任务异常：" + r.getMsg());
        }
    }


    /**
     * 同步考勤排班数据
     */
    public void syncAttendClass() {

        R r = remoteSyncAttendanceService.syncAttendClass(SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            throw new ServiceException("同步考勤排班数据定时任务异常：" + r.getMsg());
        }
    }



    /**
     * 同步考勤排班数据
     */
    public void syncAttendSchedule() {

        R r = remoteSyncAttendanceService.syncAttendSchedule(SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            throw new ServiceException("同步用户考勤排班数据定时任务异常：" + r.getMsg());
        }
    }


    /**
     * 同步考勤排班数据
     */
    public void syncAttendUserCheck() {

        R r = remoteSyncAttendanceService.syncAttendUserCheck(SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            throw new ServiceException("同步考勤打卡数据定时任务异常：" + r.getMsg());
        }
    }


}
