package com.yooa.job.task.extend;


import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.extend.api.RemoteGoalExtendService;
import com.yooa.job.mapper.JobMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 推广目标定时任务
 */
@Component("extendGoalTask")
@AllArgsConstructor
public class ExtendGoalTask {

    private final RemoteGoalExtendService remoteGoalExtendService;
    private final JobMapper jobMapper;

    /**
     * 推广每月目标制定
     */
    public void weeksGoal() {
        remoteGoalExtendService.weeksGoal(SecurityConstants.INNER);
    }

    /**
     * 推广每年目标制定
     */
    public void yearsGoal() {
        remoteGoalExtendService.yearsGoal(SecurityConstants.INNER);
    }

    /**
     * 暂时先订时刷新粉丝登记中的客户
     */
    public void brushVermicelliServe() {
        jobMapper.brushVermicelliServe();
    }
}
