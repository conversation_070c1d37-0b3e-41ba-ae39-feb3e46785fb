<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmAnchorRewardMapper">

    <select id="getAnchorRewardMoneyRecord" resultType="com.yooa.crm.api.domain.dto.CustomerRewardDto">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(cr.add_time) = DATE(NOW()) THEN total_amount END), 0) AS todayTotalReward,
            COALESCE(SUM(CASE WHEN DATE_FORMAT(cr.add_time, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') THEN total_amount END), 0) AS monthTotalReward,
            COALESCE(SUM(cr.total_amount), 0) AS totalReward
        FROM crm_customer_reward cr
        where anchor_id = #{anchorId}
    </select>


    <select id="getCustomerTotalReward" resultType="com.yooa.crm.api.domain.vo.CustomerRewardRecordVo">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(add_time) = DATE(NOW()) THEN total_amount END), 0) AS todayTotalReward,
            COALESCE(SUM(CASE WHEN DATE_FORMAT(add_time, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') THEN total_amount END), 0) AS monthTotalReward,
            COALESCE(SUM(total_amount), 0) AS totalReward
        FROM crm_customer_reward
        WHERE customer_id = #{customerId}
    </select>
    <select id="getRewardNumByIds" resultType="com.yooa.crm.api.domain.vo.CustomerRewardNumVo">
        SELECT
            count( DISTINCT cr.customer_id ) rewardNum,
            aam.anchor_id anchorId
        FROM
            crm_anchor_account_mapping aam
            LEFT JOIN crm_customer_reward cr ON cr.anchor_id = aam.account_id
        WHERE
            aam.anchor_id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        GROUP BY aam.anchor_id
    </select>
    <select id="getRewardNumById" resultType="com.yooa.crm.api.domain.vo.CustomerRewardNumVo">
        SELECT
        count( DISTINCT cr.customer_id ) rewardNum,
        aam.anchor_id
        FROM
        crm_anchor_account_mapping aam
        INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
        INNER JOIN crm_anchor_info ai ON ai.anchor_id = aam.anchor_id
        LEFT JOIN crm_customer_reward cr ON cr.anchor_id = aam.account_id
        WHERE
        aam.anchor_id = #{anchorId}
        GROUP BY aam.anchor_id
    </select>
</mapper>