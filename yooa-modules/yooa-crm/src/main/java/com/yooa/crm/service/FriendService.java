package com.yooa.crm.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmCustomer;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.CrmFriend;
import com.yooa.crm.api.domain.CrmFriendPollingChat;
import com.yooa.crm.api.domain.dto.CustomerStatisticsDto;
import com.yooa.crm.api.domain.dto.FriendEmployeeDto;
import com.yooa.crm.api.domain.dto.PollingFriendDto;
import com.yooa.crm.api.domain.query.*;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.extend.api.domain.dto.CommonalityDto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 好友 - 服务层
 */
public interface FriendService extends IService<CrmFriend> {

    /**
     * 好友列表
     */
    List<CrmCommonFriendVo> list(Page<CrmCommonFriendVo> page, FriendQuery query);

    Integer updateFriend(CrmFriend friend);

    /**
     * 获取好友客户列表
     *
     * @param bl true分页、false不分页(导出)
     */
    List<FriendCustomerVo> listByFriendCustomer(Page<FriendCustomerVo> page, CustomerFriendQuery query, boolean bl);

    /**
     * 新获取好友客户列表
     *
     * @param page
     * @param query
     * @param bl    true分页、false不分页(导出)
     * @return
     */
    List<FriendCustomerVo> listNewByFriendCustomer(Page<FriendCustomerVo> page, CustomerFriendQuery query, boolean bl);

    FriendMaintenanceVo joinList(Long friendId);

    List<FreeExportVo> listFreeExport(CustomerFriendQuery query);

    /**
     * 获取运营好友客户列表
     *
     * @param bl true分页、false不分页(导出)
     */
    List<OperateFriendCustomerVo> listByFriendOperateCustomer(Page<OperateFriendCustomerVo> page, OperateCustomerFriendQuery query, boolean bl);

    /**
     * 新获取运营好友客户列表
     *
     * @param page
     * @param query
     * @param bl    true分页、false不分页(导出)
     * @return
     */
    List<OperateFriendCustomerVo> listNewByFriendOperateCustomer(Page<OperateFriendCustomerVo> page, OperateCustomerFriendQuery query, boolean bl);

    /**
     * 获取注册列表
     */
    List<RegisterVo> listByRegister(Page<RegisterVo> page, CustomerFriendQuery query, boolean bl);

    /**
     * 获取优质列表
     *
     * @param page
     * @param query
     * @return
     */
    List<RegisterVo> listByQuality(Page<RegisterVo> page, CustomerFriendQuery query, boolean bl);

    /**
     * 获取好友列表
     */
    List<FriendDetailsVo> listByFriend(Page<FriendDetailsVo> page, CustomerFriendQuery query);

    /**
     * 我的工作台-计划详情/计划更新-客户列表(完成的指标由哪些客户组成)
     */
    IPage<FriendCustomerVo> customerList(Page page, CommonalityDto dto);

    /**
     * 获取好友客户详情
     */
    FriendCustomerDetailsVo getFriendCustomerInfo(Long friendId);

    // 客户统计-各地区人数分布
    List<GroupPeopleNumberVo> districtPeopleNumber(CustomerStatisticsDto dto);

    // 客户统计-地区时间新增人数分布
    List<GroupPeopleNumberVo> districtAddPeople(CustomerStatisticsDto dto);

    // 客户统计-各级别人数分布
    List<GroupPeopleNumberVo> rankPeopleNumber(CustomerStatisticsDto dto);

    // 客户统计-各性别人数分布
    List<GroupPeopleNumberVo> sexPeopleNumber(CustomerStatisticsDto dto);

    // 客户统计-客户年龄段
    List<SexAgePeopleNumberVo> agePeopleNumber(CustomerStatisticsDto dto);

    /**
     * 好友绑定客户
     */
    int bindCustomer(List<Long> pdUserId, Long friendId, CrmCustomer customer);

    // 我的工作台-我的客户-查询我的客户等级情况
    Map<String, Integer> selMycustomerRank(LocalDateTime beginTime, LocalDateTime endTime);


    // 推广员工详情-所有指标数据
    List<FriendEmployeeVo> selFriendEmployeeData(FriendEmployeeDto dto);

    // 扫描好友的修改时间是否大于15/30天为更新,由领取变为空闲
    int ProbationPeriod();

    // 查询客户ID集关联的好友,好友下的所有客户ID
    List<CrmCustomerFriend> selCustomerFriendList(List<Long> ids);

    // 查询用户的好友数、注册数
    List<FriendRegisterChargeVo> getFriendRegister(List<Long> ids, String beginTime, String endTime);

    // 查询部门的好友、注册数
    List<FriendRegisterChargeVo> getFriendRegisterDept(Long deptId, Long parentId, Integer hierarchy, String beginTime, String endTime);

    // 查询部门的好友、注册数
    List<FriendRegisterChargeVo> getFriendRegisterDeptTimeGroup(Long deptId, String beginTime, String endTime, Integer expType);

    // 查询用户好友、注册数
    List<FriendRegisterChargeVo> getFriendRegisterUserTimeGroup(List<Long> ids, String beginTime, String endTime, Integer expType);

    // 查询用户的好友、注册数排名
    Map<String, List<FriendRegisterChargeVo>> getFriendRegisterRanking(List<Long> ids, String beginTime, String endTime);

    // 查询部门的好友、注册数
    Map<String, List<FriendRegisterChargeVo>> getFriendRegisterDeptRanking(Long findInSetDeptId, Integer hierarchy, String beginTime, String endTime);

    List<CrmCommonFriendVo> selReceiveCommonFriend(Page<CrmCommonFriendVo> page, FriendQuery query, Long userId);

    /**
     * 轮询客户列表
     */
    List<PollingFriendVo> pollingFriendList(Page<PollingFriendVo> page, PollingFriendQuery query);

    /**
     * 轮询无效列表
     */
    List<PollingFriendInvalidVo> pollingInvalidList(Page<PollingFriendInvalidVo> page, PollingFriendQuery query);

    /**
     * 轮询审核
     */
    int pollingState(String reviewRemark, Long friendId, Long state);

    /**
     * 轮询导入
     */
    String pollingImport(List<PollingFriendDto> pollingFriendList, boolean updateSupport);

    Boolean pollingEdit(CrmFriend friend);

    /**
     * 聊天记录列表
     */
    List<CrmFriendPollingChat> chatList(String pollingApiUserId);

    List<PollingStatisticsVo> statisticsList(Page<PollingStatisticsVo> page, PollingFriendQuery query);

    /**
     * 获取VIP充值订单
     */
    List<VipRechargeOrderVo> getVipRechargeOrder(Page page,VipRechargeOrderQuery query);

    /**
     * 批量确认
     */
    String pollingBatchConfirm(LocalDate[] recordDate,String businessType);
}
