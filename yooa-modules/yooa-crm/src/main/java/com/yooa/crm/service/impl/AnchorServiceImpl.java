package com.yooa.crm.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.common.mybatis.base.QueryEntity;
import com.yooa.crm.api.domain.*;
import com.yooa.crm.api.domain.dto.AnchorInfoDto;
import com.yooa.crm.api.domain.query.*;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.crm.convert.AnchorInfoConvert;
import com.yooa.crm.mapper.*;
import com.yooa.crm.service.AnchorService;
import com.yooa.crm.service.AnchorStyleService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 主播 - 业务实现层
 */
@AllArgsConstructor
@Service
public class AnchorServiceImpl extends ServiceImpl<CrmAnchorMapper, CrmAnchor>
        implements AnchorService {

    private final CrmAnchorAccountMappingMapper crmAnchorAccountMappingMapper;

    private final CrmAnchorMapper crmAnchorMapper;

    private final CrmAnchorInfoMapper crmAnchorInfoMapper;

    private final AnchorStyleService anchorStyleService;

    private final CrmCustomerMapper customerMapper;

    private final CrmAnchorLiveRecordMapper crmAnchorLiveRecordMapper;

    private final CrmCustomerViewLiveTimeMapper crmCustomerViewLiveTimeMapper;

    private final CrmGiftMapper crmGiftMapper;

    private final CrmNobleMapper crmNobleMapper;

    private final CrmAnchorRewardMapper crmAnchorRewardMapper;

    @Override
    public AnchorAccountVo getByAccountId(Long accountId) {
        return baseMapper.selectByAccountId(accountId);
    }

    @Override
    public List<AnchorAccountVo> listByAnchorId(Long anchorId) {
        List<CrmAnchorAccountMapping> anchorAccountMappingList = crmAnchorAccountMappingMapper.selectList(
                Wrappers.<CrmAnchorAccountMapping>lambdaQuery()
                        .eq(CrmAnchorAccountMapping::getAnchorId, anchorId)
        );
        if (CollUtil.isEmpty(anchorAccountMappingList)) {
            return Collections.emptyList();
        }
        List<Long> accountIds = anchorAccountMappingList.stream().map(CrmAnchorAccountMapping::getAccountId).toList();
        List<AnchorAccountVo> anchorAccountVoList = baseMapper.selectList(
                Wrappers.<CrmAnchor>lambdaQuery()
                        .in(CrmAnchor::getAnchorId, accountIds)
        ).stream().map(anchorAccount -> {
            AnchorAccountVo anchorAccountVo = new AnchorAccountVo();
            anchorAccountVo.setAccountId(anchorAccount.getAnchorId());
            anchorAccountVo.setAccountName(anchorAccount.getAnchorAccount());
            anchorAccountVo.setAccountNickName(anchorAccount.getAnchorName());
            anchorAccountVo.setCreateTime(anchorAccount.getCreateTime());
            return anchorAccountVo;
        }).toList();
        return anchorAccountVoList;
    }


    @Override
    public Map<Long, CrmAnchor> batchGetLiveTime(List<Long> anchorIds) {
        if (CollectionUtils.isEmpty(anchorIds)) {
            return Collections.emptyMap();
        }
        List<CrmAnchor> anchors = crmAnchorMapper.selectByAnchorAccountIds(anchorIds);
        return anchors.stream()
                .collect(Collectors.toMap(CrmAnchor::getAnchorId, Function.identity()));
    }

    @Override
    public AnchorOperateVo currentOperate(Long anchorId) {
        return crmAnchorMapper.currentOperate(anchorId);
    }

    @Override
    public RecruitOperateVo recruitOperate(Long anchorId) {
        return crmAnchorMapper.recruitOperate(anchorId);
    }

    @Override
    public List<AnchorOperateVo> getHistoryOperateTeam(Long anchorId) {
        return crmAnchorMapper.getHistoryOperateTeam(anchorId);
    }

    @Override
    public List<AnchorJoinFansVo> getAnchorJoinFansList(Page<AnchorJoinFansVo> page, AnchorJoinFansQuery anchorJoinFansQuery) {
        // 查询主播已交接的客户消费数据
        List<AnchorJoinFansVo> joinFansList = crmAnchorMapper.getAnchorJoinFansList(page, anchorJoinFansQuery);
        // 查询客户打赏当月金额 打赏总金额
        List<Long> customerIds = joinFansList.stream().map(AnchorJoinFansVo::getCustomerId).toList();
        if(CollectionUtil.isEmpty(customerIds)){
            return Collections.emptyList();
        }
        List<AnchorJoinFansVo> customerRewardAmtList = crmAnchorMapper.getCustomerRewardAmt(customerIds, anchorJoinFansQuery.getAnchorId());
        Map<Long, AnchorJoinFansVo> customerRewardAmtMap = customerRewardAmtList.stream().collect(Collectors.toMap(AnchorJoinFansVo::getCustomerId, Function.identity()));
        joinFansList.forEach(joinFans -> {
            AnchorJoinFansVo anchorJoinFansVo = customerRewardAmtMap.get(joinFans.getCustomerId());
            if (anchorJoinFansVo != null) {
                joinFans.setMonthAmt(anchorJoinFansVo.getMonthAmt());
                joinFans.setTotalAmt(anchorJoinFansVo.getTotalAmt());
            }
        });
        return joinFansList;
    }

    public AnchorRewardVo getAnchorReward(Page page, AnchorRewardQuery query) throws ExecutionException, InterruptedException {
        AnchorRewardVo anchorRewardVo = new AnchorRewardVo();

        // 获取基础数据
        AnchorRewardCollectVo collectVo = crmAnchorMapper.getAnchorRewardCollect(query);
        List<AnchorVo> anchorList = crmAnchorMapper.getAnchorIds(query.getAnchorId());
        List<Long> anchorIds = anchorList.stream().map(AnchorVo::getAnchorId).toList();
        if(CollectionUtil.isEmpty(anchorIds)){
            return anchorRewardVo;
        }

        // 设置基础数据
        anchorRewardVo.setAnchorRewardCollectVo(collectVo);

        ExecutorService executor = Executors.newFixedThreadPool(15);
        try {
            // 获取打赏详情
            CompletableFuture<List<AnchorRewardDetailVo>> detailFuture = CompletableFuture.supplyAsync(
                    () -> {
                        Integer total = crmAnchorMapper.getCount(query,anchorIds);
                        int count = page.getCurrent() != 1 ? total - (Convert.toInt(page.getCurrent() - 1) * 10) : total;
                        Integer offset = count >= 10 ? count - 10 : 0;
                        List<AnchorRewardDetailVo> result = crmAnchorMapper.getRewardDetailPage(offset,page.getSize(),query,anchorIds);
                        page.setRecords(result);
                        page.setTotal(total);
                        return result;
                    } , executor
            );

            // 处理每条打赏记录
            CompletableFuture<List<AnchorRewardInfoVo>> processedDetailsFuture = detailFuture.thenApplyAsync(details -> {
                Map<Long, AnchorVo> anchorMap = anchorList.stream()
                        .collect(Collectors.toMap(AnchorVo::getAnchorId, Function.identity()));

                // 使用并行流处理
                return details.parallelStream()
                        .map(detail -> processSingleRewardAsync(detail, anchorMap, executor))
                        .toList()
                .stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());
            }, executor);

            processedDetailsFuture.thenAccept(result -> {
                anchorRewardVo.setAnchorRewardInfoList(result);
                anchorRewardVo.setTotal(page.getTotal());
            }).get();

        } finally {
            executor.shutdown();
        }

        return anchorRewardVo;
    }

    /**
     * 异步处理单个打赏记录
     */
    private CompletableFuture<AnchorRewardInfoVo> processSingleRewardAsync(
            AnchorRewardDetailVo detail,
            Map<Long, AnchorVo> anchorMap,
            Executor executor) {
        return CompletableFuture.supplyAsync(() -> {
            AnchorRewardInfoVo infoVo = new AnchorRewardInfoVo();

            // 异步获取客户名称
            CompletableFuture<String> nameFuture = CompletableFuture.supplyAsync(
                    () ->Optional.ofNullable(customerMapper.selectOne(new LambdaQueryWrapper<CrmCustomer>()
                                    .select(CrmCustomer::getCustomerName)
                                    .eq(CrmCustomer::getCustomerId, detail.getCustomerId())))
                            .map(CrmCustomer::getCustomerName)
                            .orElse(""),executor);

            // 收支行为
            String action = detail.getAction();
            String giftId = detail.getGiftId();
            // 收支行为为赠送礼物  查询礼物表
            if(StrUtil.equals(action,"sendgift")){
                Optional.ofNullable(crmGiftMapper.selectOne(new LambdaQueryWrapper<CrmGift>()
                        .eq(CrmGift::getGiftId, giftId)))
                        .map(CrmGift::getGiftName)
                        .ifPresent(infoVo::setGiftName);
            }
            // 收支行为为开通贵族 查贵族表
            if(StrUtil.equals(action,"buynoble")) {
                Optional.ofNullable(crmNobleMapper.selectOne(new LambdaQueryWrapper<CrmNoble>()
                        .eq(CrmNoble::getNobleId, giftId)))
                        .map(CrmNoble::getNobleName)
                        .ifPresent(infoVo::setGiftName);
            }

            // 填充其他字段
            nameFuture.thenApply(name -> {
                infoVo.setCustomerName(name);
                processLiveDetails(infoVo, detail, anchorMap);
                return infoVo;
            }).join();
            return infoVo;
        }, executor);
    }

    // 填充数据
    private void processLiveDetails(AnchorRewardInfoVo infoVo,
                                    AnchorRewardDetailVo detail,

                                    Map<Long, AnchorVo> anchorMap) {
        infoVo.setCustomerId(detail.getCustomerId());
        infoVo.setRewardMoney(detail.getRewardMoney());
        infoVo.setAnchorId(detail.getAnchorId());
        infoVo.setFriendId(detail.getFriendId());
        infoVo.setRewardTime(detail.getAddTime());
        infoVo.setAction(detail.getAction());
        AnchorVo anchor = anchorMap.get(detail.getAnchorId());
        if (anchor != null) {
            infoVo.setAnchorName(anchor.getAnchorName());
            infoVo.setAnchorAccountId(anchor.getPdAnchorId());
        }
    }


    @Override
    public List<AnchorManageVo> getAnchorDetailExport(Page<AnchorDetailVo> page, AnchorDetailQuery anchorDetailQuery) {
        page.setSize(-1);
        List<AnchorManageVo> anchorDetail = crmAnchorMapper.getAnchorDetail(page, anchorDetailQuery);
        anchorDetail.forEach(anchorManageVo -> {
            anchorStyleService.getOptById(anchorManageVo.getAnchorStyle()).ifPresent(anchorStyle -> anchorManageVo.setAnchorStyle(anchorStyle.getStyleName()));
        });
        return anchorDetail;
    }

    @Override
    public String anchorInfoImport(MultipartFile file) throws Exception {
        int failureNum = 0;
        StringBuilder failureMsg = new StringBuilder();
        if (!file.isEmpty()) {
            //文件名称
            int begin = Objects.requireNonNull(file.getOriginalFilename()).indexOf(".");
            //文件名称长度
            int last = file.getOriginalFilename().length();
            //判断文件格式是否正确
            String fileName = file.getOriginalFilename().substring(begin, last);
            if (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
                System.out.println("上传文件格式不正确,只支持xls、xlsx文件");
            }
        } else {
            throw new IllegalArgumentException("excel格式错误");
        }
        ImportParams importParams = new ImportParams();
        //表头(列字段占几行)
        importParams.setHeadRows(1);
        // 校验Excel文件，去掉空行
        importParams.setNeedVerify(true);
        //设置读取行数(默认从0开始)
        importParams.setReadRows(299);
        //流
        InputStream inputStream = file.getInputStream();
        //二.获取excel中的数据
        List<AnchorInfoDto> anchorInfoDtoList = ExcelImportUtil.importExcel(inputStream, AnchorInfoDto.class, importParams);
        anchorInfoDtoList = anchorInfoDtoList.stream().filter(b -> !BeanUtil.isEmpty(b)).toList();
        //三.获到正确的数据，并把它们保存到数据库
        for (AnchorInfoDto anchorInfoDto : anchorInfoDtoList) {
            try {
                //身份证号已经存在
                Long count = crmAnchorInfoMapper.selectCount(new LambdaQueryWrapper<CrmAnchorInfo>()
                        .eq(CrmAnchorInfo::getIdCardNumber, anchorInfoDto.getIdCardNumber()));
                if (count > 0) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "身份证号" + anchorInfoDto.getIdCardNumber() + "已存在";
                    failureMsg.append(msg);
                } else {
                    CrmAnchorInfo crmAnchorInfo = AnchorInfoConvert.INSTANCE.anchorInfoDtoToCrmAnchorInfo(anchorInfoDto);
                    crmAnchorInfoMapper.insert(crmAnchorInfo);
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "姓名" + anchorInfoDto.getAnchorName() + " 导入失败：";
                failureMsg.append(msg);
                log.error(msg, e);
            }
        }
        return StrUtil.isBlank(failureMsg.toString()) ? "导入完成" : failureMsg.toString() ;
    }

@Override
@DataScope(deptAlias = "d", userAlias = "u")
public List<AnchorVo> getAnchorList(Page page, AnchorQuery anchorQuery) throws ExecutionException, InterruptedException {
    // 获取主播id
    List<Long> ids = crmAnchorMapper.getAnchorIdList(page, anchorQuery);
    if (CollectionUtils.isEmpty(ids)) {
        return Collections.emptyList();
    }

    Executor executor = Executors.newFixedThreadPool(5);

    CompletableFuture<Map<Long, AnchorDataVo>> anchorDataFuture = CompletableFuture
            .supplyAsync(() -> crmAnchorMapper.getAnchorData(ids).stream().collect(Collectors.toMap(AnchorDataVo::getAnchorId,Function.identity())), executor);

    CompletableFuture<Map<Long, MaxAnchorDataVo>> maxAnchorDataFuture = CompletableFuture
            .supplyAsync(() -> crmAnchorMapper.getMaxAnchorData(ids).stream().collect(Collectors.toMap(MaxAnchorDataVo::getAnchorId,Function.identity())), executor);

    CompletableFuture<Map<Long, FansCollectVo>> fansCollectFuture = CompletableFuture
            .supplyAsync(() -> crmAnchorMapper.getFansCollect(ids).stream().collect(Collectors.toMap(FansCollectVo::getAnchorId,Function.identity())), executor);

    CompletableFuture<Map<Long, CustomerRewardNumVo>> rewardNumFuture = CompletableFuture
            .supplyAsync(() -> crmAnchorRewardMapper.getRewardNumByIds(ids).stream().collect(Collectors.toMap(CustomerRewardNumVo::getAnchorId,Function.identity())), executor);

   CompletableFuture.allOf(
            anchorDataFuture, maxAnchorDataFuture, fansCollectFuture, rewardNumFuture
    ).join();

    Map<Long, AnchorDataVo> anchorDataMap = anchorDataFuture.get();
    Map<Long, MaxAnchorDataVo> maxAnchorMap = maxAnchorDataFuture.get();
    Map<Long, FansCollectVo> fansCollectMap = fansCollectFuture.get();
    Map<Long, CustomerRewardNumVo> rewardNumMap = rewardNumFuture.get();

    // 处理组装数据
    return ids.parallelStream()
            .map(anchorId -> handlerAnchorVo(
                    anchorId,
                    anchorDataMap.get(anchorId),
                    maxAnchorMap.get(anchorId),
                    fansCollectMap.get(anchorId),
                    rewardNumMap.get(anchorId)
            ))
            .collect(Collectors.toList());
}

    // 组装AnchorVo对象
    private AnchorVo handlerAnchorVo(Long anchorId,
                                      AnchorDataVo data,
                                      MaxAnchorDataVo maxData,
                                      FansCollectVo fans,
                                      CustomerRewardNumVo rewardNum) {
        AnchorVo anchorVo = new AnchorVo();
        anchorVo.setAnchorId(anchorId);

        // 1. 处理账号数据统计
        if (data != null) {
            anchorVo.setReward(data.getReward());
            anchorVo.setLiveHours(data.getLiveHours());
            anchorVo.setJoinFansRate(data.getJoinFansRate());
            anchorVo.setJoinFans(data.getJoinFans());
            anchorVo.setFirstChargeRate(data.getFirstChargeRate());
            anchorVo.setFirstCharge(data.getFirstCharge());
            anchorVo.setLiveStatus(data.getLiveStatus());
            anchorVo.setTotalReward(data.getTotalReward());

            // 计算打赏率
            if (rewardNum != null) {
                calculateRewardRate(anchorVo, data, rewardNum);
            }
        }

        // 2. 处理基础信息
        if (maxData != null) {
            handlerBaseInfo(anchorVo, maxData);
        }

        // 3. 处理粉丝数据
        if (fans != null) {
            handlerFansInfo(anchorVo, fans);
        }

        return anchorVo;
    }

    // 计算打赏率
    private void calculateRewardRate(AnchorVo anchorVo, AnchorDataVo data, CustomerRewardNumVo rewardNum) {
        BigDecimal rewardNumBig = Convert.toBigDecimal(rewardNum.getRewardNum());
        BigDecimal joinFansBig = Convert.toBigDecimal(data.getJoinFans());

        if (rewardNumBig != null && joinFansBig != null
                && rewardNumBig.compareTo(BigDecimal.ZERO) != 0
                && joinFansBig.compareTo(BigDecimal.ZERO) != 0) {

            BigDecimal rewardRate = rewardNumBig.divide(joinFansBig, 2, RoundingMode.HALF_UP);
            anchorVo.setRewardRate(rewardRate);
        }
    }

    // 填充基础信息
    private void handlerBaseInfo(AnchorVo anchorVo, MaxAnchorDataVo maxData) {
        anchorVo.setAnchorName(maxData.getAnchorName());
        anchorVo.setPdAnchorId(maxData.getPdAnchorId());
        anchorVo.setAnchorType(maxData.getAnchorType());
        anchorVo.setAncestorsNames(maxData.getAncestorsNames());
        anchorVo.setUserStatus(maxData.getUserStatus());
        anchorVo.setOperationName(maxData.getOperationName());
        anchorVo.setOperateId(maxData.getOperateId());
       // anchorVo.setMinimumAmt(maxData.getMinimumAmt());
        anchorVo.setLanguage(maxData.getLanguage());
        anchorVo.setSex(maxData.getSex());
        anchorVo.setDeptName(maxData.getDeptName());
      //  anchorVo.setCloudAccount(maxData.getCloudAccount());
        anchorVo.setAnchorNickName(maxData.getAnchorNickName());
        anchorVo.setLiveRole(maxData.getLiveRole());
        anchorVo.setPdAnchorName(maxData.getPdAnchorName());
    }

    // 填充粉丝信息
    private void handlerFansInfo(AnchorVo anchorVo, FansCollectVo fans) {
        anchorVo.setFans1h(fans.getFans1h());
        anchorVo.setFans2h(fans.getFans2h());
        anchorVo.setFans5k(fans.getFans5k());
        anchorVo.setFans5w(fans.getFans5w());
        anchorVo.setFans10w(fans.getFans10w());
    }


    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public AnchorCollectVo getAnchorCollect(QueryEntity query) {
            return crmAnchorMapper.getAnchorCollect(query);
    }


    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<AnchorVo> getAnchorListExport(Page page, AnchorQuery anchorQuery) {
        // 获取数据
        List<AnchorVo> anchorList = crmAnchorMapper.getAnchorList(page, anchorQuery);

        if (CollectionUtils.isEmpty(anchorList)) {
            return Collections.emptyList();
        }

        List<Long> idsList = anchorList.stream()
                .map(AnchorVo::getAnchorId)
                .collect(Collectors.toList());

        Map<Long, AnchorVo> anchorMap = anchorList.stream()
                .collect(Collectors.toMap(AnchorVo::getAnchorId, Function.identity()));

        List<List<Long>> splitList = CollectionUtil.split(idsList, 100);
        Executor executor = Executors.newFixedThreadPool(20);

        // 获取主播打赏人数 关联打赏表去查会超时 只能查出id后再单独去查
        List<CompletableFuture<List<CustomerRewardNumVo>>> futures = splitList.stream()
                .map(ids -> CompletableFuture.supplyAsync(
                        () -> crmAnchorRewardMapper.getRewardNumByIds(ids),
                        executor
                ))
                .toList();

        CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        ).join();

        Map<Long, BigDecimal> rewardNumMap = futures.stream()
                .flatMap(future -> {
                    try {
                        return future.get().stream();
                    } catch (Exception e) {
                        log.error("获取打赏数据失败", e);
                        return Stream.empty();
                    }
                })
                .collect(Collectors.toMap(
                        CustomerRewardNumVo::getAnchorId,
                        vo -> Convert.toBigDecimal(vo.getRewardNum()),
                        (v1, v2) -> v1
                ));

        calculateRewardRate(anchorMap, rewardNumMap);

        return anchorList;
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public AnchorListVo getAnchorListCollect(AnchorQuery anchorQuery) {
        AnchorListVo anchorListVo = new AnchorListVo();

        // 新增求和一行
        // 粉丝达成之和
        AnchorSumFansCollectVo sumFansCollectVo = crmAnchorMapper.getSumFansCollect(anchorQuery);

        // 基础数据之和
        AnchorSumDataCollectVo sumDataCollectVo = crmAnchorMapper.getSumAnchorDataCollect(anchorQuery);
        anchorListVo.setAnchorSumFansCollectVo(sumFansCollectVo);
        anchorListVo.setAnchorSumDataCollectVo(sumDataCollectVo);
        return anchorListVo;
    }


    // 计算打赏率
    private void calculateRewardRate(Map<Long, AnchorVo> anchorMap, Map<Long, BigDecimal> rewardNumMap) {
        anchorMap.forEach((anchorId, anchorVo) -> {
            // 获取主播的打赏人数
            BigDecimal rewardNum = rewardNumMap.get(anchorId);
            if (rewardNum == null) {
                rewardNum = BigDecimal.ZERO;
            }

            // 获取接粉数
            BigDecimal joinFans = Optional.ofNullable(anchorVo.getJoinFans())
                    .map(BigDecimal::new)
                    .orElse(BigDecimal.ZERO);

            // 计算打赏率
            if (joinFans.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal rewardRate = rewardNum.divide(joinFans, 2, RoundingMode.HALF_UP);
                anchorVo.setRewardRate(rewardRate);
            } else {
                anchorVo.setRewardRate(BigDecimal.ZERO);
            }
        });
    }


    @Override
    public AnchorDetailVo getAnchorDetail(Page<AnchorDetailVo> page, AnchorDetailQuery anchorDetailQuery) {
        AnchorDetailVo anchorDetailVo = new AnchorDetailVo();
        // 传入startTime endTime为空 不查当日当月直播时长和打赏数据
        List<AnchorManageVo> anchorManageVoList = crmAnchorMapper.getAnchorDetail(page, anchorDetailQuery);

        if (CollectionUtil.isEmpty(anchorManageVoList)) {
            return anchorDetailVo;
        }

        // 主播数据统计
        anchorDetailVo.setMyAnchorCollectVo(crmAnchorMapper.getMyAnchorCollect(anchorDetailQuery.getOperateId()));
        // 运营下面的主播所有累加的打赏和直播时间那些 不需要根据分页去做
//        AnchorDetailVo rewardAndLiveHour = crmAnchorMapper.getRewardAndLiveHour(anchorDetailQuery.getOperateId());
//        BeanUtil.copyProperties(rewardAndLiveHour, anchorDetailVo);
        anchorDetailVo.setAnchorManageVoList(anchorManageVoList);
        anchorDetailVo.setTotal(page.getTotal());
        return anchorDetailVo;
    }


    /**
     * 通用打赏金额计算
     */
    private BigDecimal calculateTotalReward(List<AnchorManageVo> list, Function<AnchorManageVo, BigDecimal> mapper) {
        return list.stream()
                .map(vo -> Optional.ofNullable(mapper.apply(vo)).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 通用直播时长计算
     */
    private BigDecimal calculateTotalLiveHour(List<AnchorManageVo> list, Function<AnchorManageVo, String> fieldMapper) {
        return list.stream()
                .map(vo -> Convert.toBigDecimal(fieldMapper.apply(vo), BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}




