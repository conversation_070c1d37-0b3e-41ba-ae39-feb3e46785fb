package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.mybatis.base.QueryEntity;
import com.yooa.crm.api.domain.CrmAnchor;
import com.yooa.crm.api.domain.query.AnchorDetailQuery;
import com.yooa.crm.api.domain.query.AnchorJoinFansQuery;
import com.yooa.crm.api.domain.query.AnchorQuery;
import com.yooa.crm.api.domain.query.AnchorRewardQuery;
import com.yooa.crm.api.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 主播 - 数据层
 */
public interface CrmAnchorMapper extends BaseMapper<CrmAnchor> {

    AnchorAccountVo selectByAccountId(@Param("accountId") Long accountId);

    List<CrmAnchor> selectByAnchorAccountIds(@Param("anchorAccountIds") List<Long> anchorAccountIds);

    AnchorOperateVo currentOperate(@Param("anchorId") Long anchorId);

    RecruitOperateVo recruitOperate(@Param("anchorId") Long anchorId);

    List<AnchorOperateVo> getHistoryOperateTeam(@Param("anchorId") Long anchorId);

    /**
     * 获取工作台我的主播基本数据
     */
    List<AnchorManageVo> getAnchorDetail(Page page, @Param("anchorDetailQuery") AnchorDetailQuery anchorDetailQuery);

    /**
     * 粉丝交接
     */
    List<AnchorJoinFansVo> getAnchorJoinFansList(Page<AnchorJoinFansVo> page, @Param("anchorJoinFansQuery") AnchorJoinFansQuery anchorJoinFansQuery);


    /**
     * 查询运营旗下所有主播的直播时长和打赏之和
     * @param operateId 运营id
     * @return
     */
    AnchorDetailVo getRewardAndLiveHour(@Param("operateId") Long operateId);

    /**
     * 获取打赏列表
     */
    List<AnchorRewardInfoVo> getAnchorReward(Page<AnchorRewardVo>page, @Param("anchorRewardQuery") AnchorRewardQuery anchorRewardQuery);

    /**
     * 获取打赏数据汇总
     */
    AnchorRewardCollectVo getAnchorRewardCollect(@Param("query") AnchorRewardQuery query);

    /**
     * 获取主播列表
     */
    List<AnchorVo> getAnchorList(Page page, @Param("query") AnchorQuery anchorQuery);

    /**
     * 主播列表数据汇总
     */
    AnchorCollectVo getAnchorCollect(@Param("query") QueryEntity query);

    List<AnchorJoinFansVo> getCustomerRewardAmt(@Param("ids") List<Long> customerIds, @Param("anchorId") Long anchorId);

    List<AnchorVo> getAnchorIds(@Param("anchorId") Long anchorId);

    List<AnchorRewardDetailVo> getRewardDetail(Page<AnchorRewardVo> page,@Param("anchorRewardQuery") AnchorRewardQuery query, @Param("ids") List<Long> anchorIds);

    List<AnchorLiveDetailVo> getAnchorLiveRecord(@Param("ids") List<Long> anchorIds);

    /**
     * 根据主播id获取打赏数据在主播主播时间段的直播数据
     */
    AnchorLiveDetailVo getAnchorLiveRecordByAnchorId(@Param("anchorId") Long anchorId,@Param("addTime") LocalDateTime addTime);

    /**
     * 清空主播表打赏数据统计
     * @param anchorIds
     */
    void updateRewardMoneyByAnchorIds(@Param("ids") List<Long> anchorIds);

    Integer getCount(@Param("anchorRewardQuery") AnchorRewardQuery query, @Param("ids") List<Long> anchorIds);

    List<AnchorRewardDetailVo> getRewardDetailPage(@Param("offset") Integer offset, @Param("count") Long count, @Param("anchorRewardQuery") AnchorRewardQuery query, @Param("ids") List<Long> anchorIds);

    List<Long> getAnchorIdsByName(@Param("queryId") String queryId);

    List<Long> getAnchorIdList(Page page,@Param("query") AnchorQuery anchorQuery);

    List<AnchorDataVo> getAnchorData(@Param("ids") List<Long> ids);

    List<MaxAnchorDataVo> getMaxAnchorData(@Param("ids") List<Long> ids);

    List<FansCollectVo> getFansCollect(@Param("ids") List<Long> ids);

    MyAnchorCollectVo getMyAnchorCollect(@Param("operateId") Long operateId);

    AnchorSumFansCollectVo getSumFansCollect(@Param("query") AnchorQuery query);

    AnchorSumDataCollectVo getSumAnchorDataCollect(@Param("query") AnchorQuery anchorQuery);
}




