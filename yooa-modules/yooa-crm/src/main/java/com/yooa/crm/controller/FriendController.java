package com.yooa.crm.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.idempotent.annotation.Idempotent;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.utils.ExcelConvertUtil;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.crm.api.domain.CrmCustomer;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.CrmFriend;
import com.yooa.crm.api.domain.dto.CustomerStatisticsDto;
import com.yooa.crm.api.domain.dto.FriendEmployeeDto;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.api.domain.query.FriendQuery;
import com.yooa.crm.api.domain.query.OperateCustomerFriendQuery;
import com.yooa.crm.api.domain.query.VipRechargeOrderQuery;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.service.CustomerFriendService;
import com.yooa.crm.service.CustomerService;
import com.yooa.crm.service.FriendService;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.system.api.RemoteUserService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 好友 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/friend")
public class FriendController extends BaseController {

    private final FriendService friendService;
    private final CustomerService customerService;
    private final CustomerFriendService customerFriendService;
    private final CrmCustomerFriendMapper customerFriendMapper;
    private final RemoteUserService remoteUserService;

    /**
     * 好友列表(客户管理-公海客户-客户列表)
     */
    @GetMapping("/list")
    public AjaxResult list(Page<CrmCommonFriendVo> page, @Valid FriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.list(page, query)));
    }

    /**
     * 已领取公海客户列表
     */
    @GetMapping("/selReceiveCommonFriend")
    public AjaxResult selReceiveCommonFriend(Page<CrmCommonFriendVo> page, @Valid FriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.selReceiveCommonFriend(page, query, SecurityUtils.getUserId())));
    }

    /**
     * 已领取公海客户列表(导出)
     */
    @PostMapping("/selReceiveCommonFriend/export")
    public void selReceiveCommonFriendExport(HttpServletResponse response, Page<CrmCommonFriendVo> page, @Valid FriendQuery query) {
        List<CrmCommonFriendVo> export = friendService.selReceiveCommonFriend(page, query, SecurityUtils.getUserId());
        page.setSize(-1);
        ExcelUtil<CrmCommonFriendVo> util = new ExcelUtil<>(CrmCommonFriendVo.class);
        util.exportExcel(response, export, "已领取公海客户列表导出");
    }

    /**
     * 导出好友列表(客户管理-公海客户-导出客户列表)
     */
    @PostMapping("/list/export")
    public void export(HttpServletResponse response, @Valid FriendQuery query) {
        List<CrmCommonFriendVo> list = friendService.list(null, query);
        ExcelConvertUtil.process(list);
        ExcelUtil<CrmCommonFriendVo> util = new ExcelUtil<>(CrmCommonFriendVo.class);
        util.exportExcel(response, list, "导出客户列表");
    }

    /**
     * 好友详情
     */
    @GetMapping("/{friendId}")
    public AjaxResult info(@PathVariable Long friendId) {
        return success(friendService.getById(friendId));
    }

    /**
     * 好友客户列表(我的工作台-我的客户-客户列表)
     */
    @GetMapping("/customer/list")
    public AjaxResult customerFriendList(Page<FriendCustomerVo> page, @Valid CustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.listByFriendCustomer(page, query, true)));
    }

    /**
     * 新好友客户列表(我的工作台-我的客户-客户列表)(中涉及的一交和二交表是新一交和二交表)
     */
    @GetMapping("/customer/newList")
    public AjaxResult customerFriendNewList(Page<FriendCustomerVo> page, @Valid CustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.listNewByFriendCustomer(page, query, true)));
    }

    /**
     * 导出好友客户列表(我的工作台-我的客户-导出客户列表)
     */
    @PostMapping("/customer/list/export")
    public void customerFriendList(HttpServletResponse response, @Valid CustomerFriendQuery query) {
        List<FriendCustomerVo> list = friendService.listByFriendCustomer(new Page<FriendCustomerVo>().setSize(-1), query, false);      // 不传分页查所有
        ExcelUtil<FriendCustomerVo> util = new ExcelUtil<>(FriendCustomerVo.class);
        util.exportExcel(response, list, "导出客户列表");
    }

    /**
     * 新导出好友客户列表(我的工作台-我的客户-导出客户列表)
     */
    @PostMapping("/customer/list/newExport")
    public void customerFriendNewList(HttpServletResponse response, @Valid CustomerFriendQuery query) {
        List<FriendCustomerVo> list = friendService.listNewByFriendCustomer(new Page<FriendCustomerVo>().setSize(-1), query, false);   // 不传分页查所有
        ExcelUtil<FriendCustomerVo> util = new ExcelUtil<>(FriendCustomerVo.class);
        util.exportExcel(response, list, "新导出客户列表");
    }

    /**
     * 客户列表详情中的交接记录
     *
     * @param friendId
     */
    @GetMapping("/receive/record/list")
    public AjaxResult joinList(Long friendId) {
        return AjaxResult.success(friendService.joinList(friendId));
    }

    /**
     * 自由导出类(导出客户列表基本信息)
     */
    @PostMapping("/free/export")
    public void freeExport(HttpServletResponse response, CustomerFriendQuery query) {
        List<FreeExportVo> list = friendService.listFreeExport(query);      // 不传分页查所有
        ExcelUtil<FreeExportVo> util = new ExcelUtil<>(FreeExportVo.class);
        util.exportExcel(response, list, "导出客户列表基本信息");
    }

    /**
     * 运营好友客户列表(我的工作台-我的客户-运营客户列表)
     */
    @GetMapping("/operateCustomer/list")
    public AjaxResult operateCustomerFriendList(Page<OperateFriendCustomerVo> page, @Valid OperateCustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.listByFriendOperateCustomer(page, query, true)));
    }

    /**
     * 新运营好友客户列表(我的工作台-我的客户-运营客户列表)
     */
    @GetMapping("/operateCustomer/newList")
    public AjaxResult operateCustomerFriendNewList(Page<OperateFriendCustomerVo> page, @Valid OperateCustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.listNewByFriendOperateCustomer(page, query, true)));
    }

    /**
     * 导出运营好友客户列表(我的工作台-我的客户-导出运营客户列表)
     */
    @PostMapping("/operateCustomer/list/export")
    public void operateCustomerFriendList(HttpServletResponse response, @Valid OperateCustomerFriendQuery query) {
        List<OperateFriendCustomerVo> list = friendService.listByFriendOperateCustomer(new Page<OperateFriendCustomerVo>().setSize(-1), query, false);      // 不传分页查所有
        ExcelUtil<OperateFriendCustomerVo> util = new ExcelUtil<>(OperateFriendCustomerVo.class);
        util.exportExcel(response, list, "导出运营客户列表");
    }

    /**
     * 新导出运营好友客户列表(我的工作台-我的客户-导出运营客户列表)
     */
    @PostMapping("/operateCustomer/list/newExport")
    public void operateCustomerFriendNewList(HttpServletResponse response, @Valid OperateCustomerFriendQuery query) {
        List<OperateFriendCustomerVo> list = friendService.listNewByFriendOperateCustomer(new Page<OperateFriendCustomerVo>().setSize(-1), query, false);      // 不传分页查所有
        ExcelUtil<OperateFriendCustomerVo> util = new ExcelUtil<>(OperateFriendCustomerVo.class);
        util.exportExcel(response, list, "导出运营客户列表");
    }

    /**
     * 注册列表(我的工作台-我的客户-注册列表)
     */
    @GetMapping("/customer/register/list")
    public AjaxResult registerList(Page<RegisterVo> page, @Valid CustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.listByRegister(page, query, true)));
    }

    /**
     * 导出注册列表(我的工作台-我的客户-导出注册列表)
     */
    @PostMapping("/customer/register/list/export")
    public void registerList(HttpServletResponse response, @Valid CustomerFriendQuery query) {
        List<RegisterVo> list = friendService.listByRegister(new Page<RegisterVo>().setSize(-1), query, false);      // 不传分页查所有
        ExcelUtil<RegisterVo> util = new ExcelUtil<>(RegisterVo.class);
        util.exportExcel(response, list, "导出注册列表");
    }

    /**
     * 优质列表(我的工作台-我的客户-优质列表)
     */
    @GetMapping("/customer/quality/list")
    public AjaxResult qualityList(Page<RegisterVo> page, @Valid CustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.listByQuality(page, query, true)));
    }

    /**
     * 导出优质列表(我的工作台-我的客户-导出优质列表)
     */
    @PostMapping("/customer/quality/list/export")
    public void qualityList(HttpServletResponse response, @Valid CustomerFriendQuery query) {
        List<RegisterVo> list = friendService.listByQuality(new Page<RegisterVo>().setSize(-1), query, false);      // 不传分页查所有
        ExcelUtil<RegisterVo> util = new ExcelUtil<>(RegisterVo.class);
        util.exportExcel(response, list, "导出注册列表");
    }

    /**
     * 好友列表(我的工作台-我的客户-好友列表)
     */
    @GetMapping("/customer/friend/list")
    public AjaxResult friendList(Page<FriendDetailsVo> page, CustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(friendService.listByFriend(page, query)));
    }

    /**
     * 导出好友列表(我的工作台-我的客户-导出好友列表)
     */
    @PostMapping("/customer/friend/list/export")
    public void friendList(HttpServletResponse response, CustomerFriendQuery query) {
        List<FriendDetailsVo> list = friendService.listByFriend(new Page<FriendDetailsVo>().setSize(-1), query);      // 不传分页查所有
        ExcelUtil<FriendDetailsVo> util = new ExcelUtil<>(FriendDetailsVo.class);
        util.exportExcel(response, list, "导出好友列表");
    }

    /**
     * 我的工作台-计划详情/计划更新-客户列表(完成的指标由哪些客户组成)
     */
    @GetMapping("customerList")
    public AjaxResult customerList(Page<?> page, CommonalityDto dto) {
        return AjaxResult.success(friendService.customerList(page, dto));
    }

    /**
     * 好友客户详情
     */
    @GetMapping("/customer/{friendId}")
    public AjaxResult getFriendCustomerInfo(@PathVariable Long friendId) {
        return success(friendService.getFriendCustomerInfo(friendId));
    }

    @Log(title = "好友信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CrmFriend friend) {
        friend.setStatus(DictConstants.CUSTOMER_STATUS_RECEIVED);          // 创建默认为领取
        friend.setReceiveNumber(1);                                        // 领取次数为1
        friend.setExtendId(SecurityUtils.getUserId());                     // 领取人ID
        friend.setFriendCode(UUID.randomUUID().toString());                // 好友编码,确认唯一性,用于确认领取的好友分支
        boolean save = friendService.save(friend);

        return toAjax(save);
    }

    @Log(title = "好友信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CrmFriend friend) {
        return AjaxResult.success(friendService.updateFriend(friend));
    }

    /**
     * 客户统计-各地区人数分布
     */
    @GetMapping("districtPeopleNumber")
    public AjaxResult districtPeopleNumber(CustomerStatisticsDto dto) {
        return AjaxResult.success(friendService.districtPeopleNumber(dto));
    }

    /**
     * 客户统计-地区时间新增人数分布
     */
    @GetMapping("districtAddPeople")
    public AjaxResult districtAddPeople(CustomerStatisticsDto dto) {
        return AjaxResult.success(friendService.districtAddPeople(dto));
    }

    /**
     * 客户统计-各性别人数分布
     */
    @GetMapping("sexPeopleNumber")
    public AjaxResult sexPeopleNumber(CustomerStatisticsDto dto) {
        return AjaxResult.success(friendService.sexPeopleNumber(dto));
    }

    /**
     * 客户统计-各级别 人数分布
     */
    @GetMapping("rankPeopleNumber")
    public AjaxResult rankPeopleNumber(CustomerStatisticsDto dto) {
        return AjaxResult.success(friendService.rankPeopleNumber(dto));
    }

    /**
     * 客户统计-客户年龄段
     */
    @GetMapping("agePeopleNumber")
    public AjaxResult agePeopleNumber(CustomerStatisticsDto dto) {
        return AjaxResult.success(friendService.agePeopleNumber(dto));
    }

    @Log(title = "好友领取", businessType = BusinessType.UPDATE)
    @PostMapping("/receive/{friendId}")
    public AjaxResult receive(@PathVariable Long friendId) {
        CrmFriend beforeFriend = friendService.getById(friendId);
        if (ObjUtil.isNull(beforeFriend)) {
            return warn(friendId + "信息不存在");
        }

        if (!beforeFriend.getStatus().equals(DictConstants.CUSTOMER_STATUS_LOSE)) {
            throw new ServiceException("用户状态未为流失状态,无法领取");
        }

        friendService.update(new UpdateWrapper<CrmFriend>()
                .eq("friend_id", friendId)
                .set("status", DictConstants.CUSTOMER_STATUS_SEAL));    // 原好友状态改为封存

        CrmFriend afterFriend = BeanUtil.copyProperties(beforeFriend, CrmFriend.class);

        afterFriend.setFriendId(null);                                          // id滞空
        afterFriend.setExtendId(SecurityUtils.getUserId());                     // 领取人id
        afterFriend.setStatus(DictConstants.CUSTOMER_STATUS_RECEIVED);          // 领取的好友状态为领取
        afterFriend.setReceiveNumber(afterFriend.getReceiveNumber() + 1);       // 领取次数+1

        return toAjax(friendService.save(afterFriend));                         // 领取(原好友封存、建立一个新好友)
    }


    @Log(title = "客户绑定", businessType = BusinessType.UPDATE)
    @Idempotent(expireTime = 10)
    @PostMapping("/bind/{friendId}/{customerId}")
    public AjaxResult bindCustomer(@PathVariable Long friendId, @PathVariable Long customerId) {

        List<Long> pyExtendIds = remoteUserService.getUserById(SecurityUtils.getUserId(), SecurityConstants.INNER).getData().getPdUserId();

        if (CollUtil.isEmpty(pyExtendIds)) {
            return warn("您还未绑定PYID");
        }
        CrmCustomer customer = customerService.getById(customerId);

        if (ObjUtil.isNull(customer)) {
            return warn(StrUtil.format("[{}]客户不存在", customerId));
        }
        if (!pyExtendIds.contains(customer.getExtendId())) {
            return warn(StrUtil.format("[{}]可能不是您的客户", customerId));
        }

        // 好友信息
        CrmFriend friend = friendService.getById(friendId);

        if (friend.getRecordDate().isAfter(customer.getUpdateTime().toLocalDate())) {
            return warn(StrUtil.format("[{}]客户更新时间不能早于加好友时间", customerId));
        }

        // 查询该客户在绑的记录
        CrmCustomerFriend customerFriend = customerFriendService.getBindByCustomerId(customerId);                                          // 好友和客户绑定记录

        // 若有在绑的记录
        if (ObjUtil.isNotNull(customerFriend)) {
            if (customerFriend.getFriendId().equals(friendId)) {
                // 好友相同
                return warn(StrUtil.format("[{}]客户已被该好友绑定，请勿重复绑定", customerId));
            }
            CrmFriend oldFriend = friendService.getById(customerFriend.getFriendId());
            // 好友不同,领取人相同
            if (oldFriend.getExtendId().equals(friend.getExtendId())) {
                // 好友绑定客户时的客户更新时间与当前客户的更新时间
                if (customerFriend.getBeginTime().toLocalDate().isEqual(customer.getUpdateTime().toLocalDate())) {
                    return warn(StrUtil.format("[{}]客户已在好友[{}]名下，且更新时间[{}]并无变动，不符合激活规则", customerId, oldFriend.getFriendName(), customer.getUpdateTime()));
                }
            }
        }

        // 好友下已绑定的客户
        List<CrmCustomerFriend> friendCustomerList = customerFriendService.getBindByFriendId(friendId);

        for (CrmCustomerFriend fc : friendCustomerList) {
            if (!fc.getBeginTime().toLocalDate().isEqual(customer.getUpdateTime().toLocalDate())) {
                return warn(StrUtil.format("[{}]客户更新时间与好友名下其他客户更新时间不同，不满足大小号规则", customerId));
            }
        }

        // 解绑关系
        if (ObjUtil.isNotNull(customerFriend)) {
            customerFriend.setStatus(1);        // 解绑
            customerFriend.setEndTime(customer.getUpdateTime());    // 另一个人的绑定时间就是上一个人绑定结束时间
            customerFriendService.updateById(customerFriend);
        }

        return success(friendService.bindCustomer(pyExtendIds, friendId, customer));
    }

    /**
     * 我的工作台-我的客户-查询我的客户等级情况
     */
    @GetMapping("selMycustomerRank")
    public AjaxResult selMycustomerRank(@RequestParam(value = "beginTime", required = false) LocalDateTime beginTime,
                                        @RequestParam(value = "endTime", required = false) LocalDateTime endTime) {
        return AjaxResult.success(friendService.selMycustomerRank(beginTime, endTime));
    }

    /**
     * 我的工作台-我的客户-绑定时显示这个好友下已绑定的客户id集
     */
    @GetMapping("/bind/{friendId}")
    public AjaxResult selBindCustomerIds(@PathVariable Long friendId) {
        List<CrmCustomerFriend> customerFriendList = customerFriendService.getBindByFriendId(friendId);
        return success(customerFriendList.stream().map(CrmCustomerFriend::getCustomerId).toList());
    }


    /**
     * 推广员工详情-所有指标数据
     */
    @PostMapping("/selFriendEmployeeData")
    public AjaxResult selFriendEmployeeData(@RequestBody FriendEmployeeDto dto) {
        return AjaxResult.success(friendService.selFriendEmployeeData(dto));
    }

    /**
     * 扫描好友的修改时间是否大于15/30天为更新,由领取变为空闲
     */
    @InnerAuth
    @PostMapping("/ProbationPeriod")
    public R<Integer> ProbationPeriod() {
        return R.ok(friendService.ProbationPeriod());
    }

    /**
     * 查询客户ID集关联的好友,好友下的所有客户ID
     */
    @InnerAuth
    @GetMapping("/selCustomerFriendList")
    public R<List<CrmCustomerFriend>> selCustomerFriendList(@RequestParam("ids") List<Long> ids) {
        return R.ok(friendService.selCustomerFriendList(ids));
    }

    /**
     * 查询用户的好友、注册数
     *
     * @param ids       OA用户ID集
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @InnerAuth
    @GetMapping("/getFriendRegister")
    public R<List<FriendRegisterChargeVo>> getFriendRegister(@RequestParam(value = "ids", required = false) List<Long> ids,
                                                             @RequestParam(value = "beginTime", required = false) String beginTime,
                                                             @RequestParam(value = "endTime", required = false) String endTime) {
        return R.ok(friendService.getFriendRegister(ids, beginTime, endTime));
    }

    /**
     * 查询用户的好友、注册数
     *
     * @param ids       OA用户ID集
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @InnerAuth
    @GetMapping("/getFriendRegisterRanking")
    public R<Map<String, List<FriendRegisterChargeVo>>> getFriendRegisterRanking(@RequestParam(value = "ids", required = false) List<Long> ids,
                                                                                 @RequestParam("beginTime") String beginTime,
                                                                                 @RequestParam("endTime") String endTime) {
        return R.ok(friendService.getFriendRegisterRanking(ids, beginTime, endTime));
    }

    /**
     * 查询部门的好友、注册数
     *
     * @param deptId    OA部门ID
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @InnerAuth
    @GetMapping("/getFriendRegisterDept")
    public R<List<FriendRegisterChargeVo>> getFriendRegisterDept(@RequestParam(value = "deptId", required = false) Long deptId,
                                                                 @RequestParam(value = "parentId", required = false) Long parentId,
                                                                 @RequestParam(value = "hierarchy", required = false) Integer hierarchy,
                                                                 @RequestParam("beginTime") String beginTime,
                                                                 @RequestParam("endTime") String endTime) {
        return R.ok(friendService.getFriendRegisterDept(deptId, parentId, hierarchy, beginTime, endTime));
    }

    /**
     * 查询部门的好友、注册数根据时间分组
     *
     * @param deptId    OA部门ID
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @InnerAuth
    @GetMapping("/getFriendRegisterDeptTimeGroup")
    public R<List<FriendRegisterChargeVo>> getFriendRegisterDeptTimeGroup(@RequestParam("deptId") Long deptId,
                                                                          @RequestParam("beginTime") String beginTime,
                                                                          @RequestParam("endTime") String endTime,
                                                                          @RequestParam("expType") Integer expType) {
        return R.ok(friendService.getFriendRegisterDeptTimeGroup(deptId, beginTime, endTime, expType));
    }

    /**
     * 查询用户好友、注册数根据时间分组
     *
     * @param ids       OA用户ID集
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @InnerAuth
    @GetMapping("/getFriendRegisterUserTimeGroup")
    public R<List<FriendRegisterChargeVo>> getFriendRegisterUserTimeGroup(@RequestParam(value = "ids", required = false) List<Long> ids,
                                                                          @RequestParam("beginTime") String beginTime,
                                                                          @RequestParam("endTime") String endTime,
                                                                          @RequestParam("expType") Integer expType) {
        return R.ok(friendService.getFriendRegisterUserTimeGroup(ids, beginTime, endTime, expType));
    }

    /**
     * 查询部门的好友、注册数排名
     *
     * @param hierarchy 部门层级
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @InnerAuth
    @GetMapping("/getFriendRegisterDeptRanking")
    public R<Map<String, List<FriendRegisterChargeVo>>> getFriendRegisterDeptRanking(@RequestParam(value = "findInSetDeptId", required = false) Long findInSetDeptId,
                                                                                     @RequestParam("hierarchy") Integer hierarchy,
                                                                                     @RequestParam(value = "beginTime", required = false) String beginTime,
                                                                                     @RequestParam(value = "endTime", required = false) String endTime) {
        return R.ok(friendService.getFriendRegisterDeptRanking(findInSetDeptId, hierarchy, beginTime, endTime));
    }

    /**
     * VIP充值订单
     */
    @GetMapping("/getVipRechargeOrder")
    public AjaxResult getVipRechargeOrder(Page page, VipRechargeOrderQuery query) {
        return success(page.setRecords(friendService.getVipRechargeOrder(page, query)));
    }
}
