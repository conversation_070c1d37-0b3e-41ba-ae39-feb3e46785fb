package com.yooa.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.base.Preconditions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.CrmCustomerHandover;
import com.yooa.crm.api.domain.dto.ReceiveHandoverDto;
import com.yooa.crm.api.domain.dto.HandoverApplicationDto;
import com.yooa.crm.api.domain.dto.RefuseHandoverDto;
import com.yooa.crm.api.domain.query.CustomerHandoverQuery;
import com.yooa.crm.api.domain.query.CustomerSecondaryHandoverQuery;
import com.yooa.crm.api.domain.query.HandoverQuery;
import com.yooa.crm.api.domain.vo.CustomerHandoverVo;
import com.yooa.crm.api.domain.vo.CustomerSecondaryHandoverVo;
import com.yooa.crm.api.domain.vo.HandoverListVo;
import com.yooa.crm.enums.HandoverEnum;
import com.yooa.crm.enums.HandoverTypeEnum;
import com.yooa.crm.enums.PdType;
import com.yooa.crm.mapper.CrmCustomerHandoverMapper;
import com.yooa.crm.service.AnchorOperateService;
import com.yooa.crm.service.CustomerFriendService;
import com.yooa.crm.service.CustomerHandoverService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysUserVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> xh
 * @Date: 2025/6/9 14:24
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class CustomerHandoverServiceImpl extends ServiceImpl<CrmCustomerHandoverMapper, CrmCustomerHandover>
        implements CustomerHandoverService {

    private final CustomerFriendService customerFriendService;

    private final AnchorOperateService anchorOperateService;

    private final RemoteUserService remoteUserService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handoverApplication(HandoverApplicationDto handoverApplicationDto) {
        // 1.校验规则
        checkRules(handoverApplicationDto);

        // 2.组装入参实体
        CrmCustomerHandover customerHandover = buildCrmCustomerHandover(handoverApplicationDto);

        // 3.保存交接记录
        save(customerHandover);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveOperateHandover(ReceiveHandoverDto receiveHandoverDto) {
        // 根据主键id查询该条交接记录
        CrmCustomerHandover customerHandover = getCrmCustomerHandover(receiveHandoverDto.getHandoverId());

        LocalDateTime now = LocalDateTime.now();
        // 二交如果客户之前有交接成功的二交数据 更新脱手时间
        if (StrUtil.equals(customerHandover.getHandoverType(),HandoverTypeEnum.TWO_HANDOVER.getCode())) {
            // 获取客户已交接最后一条数据
            CrmCustomerHandover crmCustomerHandover = getLastHandedOver(customerHandover.getCustomerId());
            // 客户之前有已交接的数据 将之前数据更新脱手时间
            if(crmCustomerHandover != null){
                crmCustomerHandover.setLoseTime(now);
                baseMapper.updateById(crmCustomerHandover);
            }
        }

        // 该客户已交接成功条数 超过1条设置交接状态为多次交接
        Long count = getHandoverCountByCustomerId(customerHandover.getCustomerId());
        customerHandover.setHandoverNum(count >= 1 ? "2" : "1");

        // 更改交接记录状态为已交接 更新接收时间
        customerHandover.setHandoverStatus(HandoverEnum.HANDED_OVER.getCode());
        customerHandover.setReceiveTime(now);

        // 更新交接记录表
        updateById(customerHandover);

    }

    private static void checkHandoverStatus(String handoverStatus) {
        if(!StrUtil.equals(handoverStatus,HandoverEnum.WAIT_HANDOVER.getCode())){
            throw new ServiceException("该条交接记录交接状态不为待交接");
        }
    }

    /**
     * 获取交接记录
     */
    private CrmCustomerHandover getCrmCustomerHandover(Long handoverId) {
        CrmCustomerHandover customerHandover = Optional.ofNullable(getOne(new LambdaQueryWrapper<CrmCustomerHandover>()
                        .eq(CrmCustomerHandover::getHandoverId, handoverId)))
                .orElseThrow(() -> new ServiceException("未查询到交接记录"));
        // 交接状态（0待交接 1已交接 2拒绝交接） 只处理交接状态为待交接的
        if(StrUtil.equals(customerHandover.getHandoverStatus(),HandoverEnum.REFUSE_HANDOVER.getCode())){
            throw new ServiceException("该条交接记录交接状态已为拒绝");
        }

        //交接类型（1一交 2二交）一交塞运营id值  二交塞客服id值
//        String handoverType = customerHandover.getHandoverType();
//        if (StrUtil.equals(handoverType, HandoverTypeEnum.ONE_HANDOVER.getCode())) {
//            customerHandover.setOperateId(SecurityUtils.getUserId());
//        }
//        if(StrUtil.equals(handoverType,HandoverTypeEnum.TWO_HANDOVER.getDesc())){
//            customerHandover.setServeId(SecurityUtils.getUserId());
//        }
        return customerHandover;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refuseOperateHandover(RefuseHandoverDto refuseHandoverDto) {
        // 根据主键id查询该条交接记录
        CrmCustomerHandover customerHandover = getCrmCustomerHandover(refuseHandoverDto.getHandoverId());

        if (StrUtil.equals(customerHandover.getHandoverType(),HandoverTypeEnum.TWO_HANDOVER.getCode())
                && StrUtil.equals(customerHandover.getHandoverStatus(),HandoverEnum.HANDED_OVER.getCode())){
                LocalDateTime loseTime = customerHandover.getLoseTime();
                if(loseTime != null){
                    CrmCustomerHandover crmCustomerHandover = baseMapper.selectOne(new LambdaQueryWrapper<CrmCustomerHandover>()
                            .eq(CrmCustomerHandover::getCustomerId, customerHandover.getCustomerId())
                            .eq(CrmCustomerHandover::getHandoverType, HandoverTypeEnum.TWO_HANDOVER.getCode())
                            .eq(CrmCustomerHandover::getHandoverStatus, HandoverEnum.HANDED_OVER.getCode())
                            .eq(CrmCustomerHandover::getLoseTime, customerHandover.getReceiveTime()));
                    if(crmCustomerHandover != null){
                        crmCustomerHandover.setLoseTime(loseTime);
                        updateById(crmCustomerHandover);
                    }
                }
        }

        // 状态设置为拒绝交接
        customerHandover.setHandoverStatus(HandoverEnum.REFUSE_HANDOVER.getCode());
        customerHandover.setRejectInfo(refuseHandoverDto.getRejectInfo());

        updateById(customerHandover);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<HandoverListVo> getExtendHandoverList(Page page, HandoverQuery query, Long userId) {
        return baseMapper.getHandoverList(page,query, PdType.EXTEND.getCode(),userId);

    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<HandoverListVo> getOperateHandoverList(Page page, HandoverQuery query, Long userId) {
        return baseMapper.getHandoverList(page,query,PdType.OPERATE.getCode(),userId);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<HandoverListVo> getServeHandoverList(Page page, HandoverQuery query, Long userId) {
        return baseMapper.getHandoverList(page,query,PdType.SERVE.getCode(), userId);
    }

    @Override
    public void receiveServeHandover(ReceiveHandoverDto receiveHandoverDto) {

    }

    @Override
    public void refuseServeHandover(RefuseHandoverDto refuseHandoverDto) {

    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerHandoverVo> list(Page page, CustomerHandoverQuery query) {
        return baseMapper.list(page,query);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerSecondaryHandoverVo> getExtendSecondaryHandover(Page page, CustomerSecondaryHandoverQuery query) {
        return baseMapper.getExtendSecondaryHandover(page,query);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerSecondaryHandoverVo> getServeSecondaryHandover(Page page, CustomerSecondaryHandoverQuery query) {
        return baseMapper.getServeSecondaryHandover(page,query);
    }

    /**
     * 根据客户id获取已成功交接的条数
     */
    private Long getHandoverCountByCustomerId(Long customerId) {
        return getBaseMapper()
                .selectCount(new LambdaQueryWrapper<CrmCustomerHandover>()
                .eq(CrmCustomerHandover::getCustomerId, customerId)
                .eq(CrmCustomerHandover::getHandoverStatus, HandoverEnum.HANDED_OVER.getCode()));
    }

    private void checkRules(HandoverApplicationDto handoverApplicationDto) {
        // 交接类型（1一交 2二交）
        String type = handoverApplicationDto.getHandoverType();
        if (StrUtil.equals(type, HandoverTypeEnum.ONE_HANDOVER.getCode())) {
            Preconditions.checkArgument(Objects.nonNull(handoverApplicationDto.getAnchorId()),"交接主播id不可为空");
        }
        if(StrUtil.equals(type, HandoverTypeEnum.TWO_HANDOVER.getCode())) {
            Preconditions.checkArgument(Objects.nonNull(handoverApplicationDto.getServeId()),"二交客服id不可为空");
        }

        // 存在状态为待审核记录
        Long toBeReviewedCount = baseMapper.selectCount(new LambdaQueryWrapper<CrmCustomerHandover>()
                .eq(CrmCustomerHandover::getCustomerId, handoverApplicationDto.getCustomerId())
                .eq(CrmCustomerHandover::getHandoverStatus, HandoverEnum.WAIT_HANDOVER.getCode()));
        if(toBeReviewedCount > 0){
            throw new ServiceException("当前状态为审核状态 不允许重新发起交接");
        }

        // TODO 其他规则校验
    }

    private void checkHandedOverData(Long customerId, Long id, Long anchorId, String type) {
        // 存在状态为该推广已交接该主播记录
        Long handedOverCount = baseMapper.selectCount(new LambdaQueryWrapper<CrmCustomerHandover>()
                .eq(CrmCustomerHandover::getCustomerId, customerId)
                .eq(StrUtil.equals(type,HandoverTypeEnum.ONE_HANDOVER.getCode()),CrmCustomerHandover::getPyAnchorId, anchorId)
                .eq(StrUtil.equals(type,HandoverTypeEnum.ONE_HANDOVER.getCode()),CrmCustomerHandover::getPyOperateId, id)
                .eq(StrUtil.equals(type,HandoverTypeEnum.TWO_HANDOVER.getCode()),CrmCustomerHandover::getPyServeId, id)
                .eq(CrmCustomerHandover::getHandoverStatus, HandoverEnum.HANDED_OVER.getCode()));
        if(handedOverCount > 0){
            throw new ServiceException("该客户已交接  无法重新发起交接");
        }
    }

    /**
     * 构建交接记录入库参数
     */
    public CrmCustomerHandover buildCrmCustomerHandover(HandoverApplicationDto handoverApplicationDto) {
        CrmCustomerFriend customerFriend =Optional.ofNullable(customerFriendService.getBindByCustomerId(handoverApplicationDto.getCustomerId()))
                .orElseThrow(() -> new ServiceException("未查询到在绑记录"));

        // 交接类型（1一交 2二交）
        String type = handoverApplicationDto.getHandoverType();
        CrmCustomerHandover customerHandover = new CrmCustomerHandover();
        // 客户id
        customerHandover.setCustomerId(handoverApplicationDto.getCustomerId());
        // 图片
        customerHandover.setHandoverImg(handoverApplicationDto.getHandoverImg());
        // 反馈
        customerHandover.setHandoverInfo(handoverApplicationDto.getHandoverInfo());
        // 推广id
       // customerHandover.setExtendId(customerFriend.getPyExtendId());
        // 交接状态（0待交接 1已交接 2拒绝交接）
        customerHandover.setHandoverStatus(HandoverEnum.WAIT_HANDOVER.getCode());
        // 交接时间
        customerHandover.setHandoverTime(LocalDateTime.now());
        // 好友绑定表主键id
        customerHandover.setCustomerFriendId(customerFriend.getFriendId());
        // PY推广id
        customerHandover.setPyExtendId(customerFriend.getPyExtendId());
        // 交接类型（1一交 2二交）
        customerHandover.setHandoverType(type);
        List<SysUserVo> extendUserList = remoteUserService.getUserList(UserQuery.builder().pdUserId(Lists.newArrayList(customerFriend.getPyExtendId())).pdType("1").build(), SecurityConstants.INNER).getData();
        if(CollectionUtil.isEmpty(extendUserList)){
          throw new ServiceException(StrUtil.format("推广id [{}] 未绑定oa账号 ", customerFriend.getPyExtendId()));
        }
        SysUserVo sysUserVo = extendUserList.get(0);
        customerHandover.setExtendId(sysUserVo.getUserId());

        if(StrUtil.equals(type, HandoverTypeEnum.ONE_HANDOVER.getCode())){
            // 主播id
            customerHandover.setPyAnchorId(handoverApplicationDto.getAnchorId());
            // py运营id
            Long pyOperateId = anchorOperateService.getPyOperateIdByAnchorId(handoverApplicationDto.getAnchorId());
            customerHandover.setPyOperateId(pyOperateId);
            List<SysUserVo> operateUserList = remoteUserService.getUserList(UserQuery.builder().pdUserId(Lists.newArrayList(pyOperateId)).pdType("2").build(), SecurityConstants.INNER).getData();
            if(CollectionUtil.isEmpty(operateUserList)){
               throw new ServiceException(StrUtil.format("运营id [{}] 未绑定oa账号",pyOperateId));
            }
            SysUserVo operateUserVo = operateUserList.get(0);
            customerHandover.setOperateId(operateUserVo.getUserId());
            // 同一个运营已交接完成该用户 不能重复发起交接
            checkHandedOverData(handoverApplicationDto.getCustomerId(), pyOperateId, handoverApplicationDto.getAnchorId(), handoverApplicationDto.getHandoverType());
        }
        if (StrUtil.equals(type, HandoverTypeEnum.TWO_HANDOVER.getCode())){
            // 客服id
            customerHandover.setPyServeId(handoverApplicationDto.getServeId());
            List<SysUserVo> serveUserList = remoteUserService.getUserList(UserQuery.builder().pdUserId(Lists.newArrayList(handoverApplicationDto.getServeId())).pdType("7").build(), SecurityConstants.INNER).getData();
            if(CollectionUtil.isEmpty(serveUserList)){
                throw new ServiceException(StrUtil.format("客服id [{}] 未绑定oa账号", handoverApplicationDto.getServeId()));
            }
            SysUserVo serveUserVo = serveUserList.get(0);
            customerHandover.setServeId(serveUserVo.getUserId());
            // 同一个客服已交接完成该用户 不能重复发起交接
            checkHandedOverData(handoverApplicationDto.getCustomerId(), handoverApplicationDto.getServeId() ,null, handoverApplicationDto.getHandoverType());
        }
        // 该客户已交接成功条数 超过1条设置交接状态为多次交接
        Long count = getHandoverCountByCustomerId(customerHandover.getCustomerId());
        // 交接次数（1首次交接 2多次交接）
        customerHandover.setHandoverNum(count >= 1 ? "2" : "1");

        return customerHandover;
    }

    /**
     * 获取客户以前二交成功交接后的数据
     */
    private CrmCustomerHandover getLastHandedOver(Long customerId) {
        return getOne(new LambdaQueryWrapper<CrmCustomerHandover>()
                .eq(CrmCustomerHandover::getCustomerId, customerId)
                .eq(CrmCustomerHandover::getHandoverStatus, HandoverEnum.HANDED_OVER.getCode())
                .orderByAsc(CrmCustomerHandover::getHandoverTime)
                .isNull(CrmCustomerHandover::getLoseTime)
                .eq(CrmCustomerHandover::getHandoverType,HandoverTypeEnum.TWO_HANDOVER.getCode())
                .last("limit 1"));
    }
}
