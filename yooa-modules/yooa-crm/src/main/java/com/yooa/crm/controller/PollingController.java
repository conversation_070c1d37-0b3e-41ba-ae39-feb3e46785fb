package com.yooa.crm.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.utils.ExcelConvertUtil;
import com.yooa.crm.api.domain.CrmFriendPollingReview;
import com.yooa.crm.api.domain.dto.PollingFriendDto;
import com.yooa.crm.api.domain.query.PollingFriendQuery;
import com.yooa.crm.api.domain.vo.PollingFriendInvalidVo;
import com.yooa.crm.api.domain.vo.PollingFriendVo;
import com.yooa.crm.api.domain.vo.PollingStatisticsVo;
import com.yooa.crm.service.FriendPollingReviewService;
import com.yooa.crm.service.FriendService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

import static com.yooa.common.core.web.domain.AjaxResult.success;


@Slf4j
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/polling")
public class PollingController {
    private final FriendService friendService;
    private final FriendPollingReviewService friendPollingReviewService;

    /**
     * 轮询客户列表
     */
    @GetMapping("/list")
    public AjaxResult list(Page<PollingFriendVo> page, PollingFriendQuery query) {
        return success(page.setRecords(friendService.pollingFriendList(page, query)));
    }

    /**
     * 导出轮询客户列表
     */
    @PostMapping("/list/exportList")
    public void exportList(HttpServletResponse response, @Valid PollingFriendQuery query) {
        List<PollingFriendVo> list = friendService.pollingFriendList(null, query);
        ExcelConvertUtil.process(list);
        ExcelUtil<PollingFriendVo> util = new ExcelUtil<>(PollingFriendVo.class);
        util.exportExcel(response, list, "导出客户列表");
    }

    /**
     * 轮询无效列表
     */
    @GetMapping("/invalidList")
    public AjaxResult invalidList(Page<PollingFriendInvalidVo> page, PollingFriendQuery query) {
        return success(page.setRecords(friendService.pollingInvalidList(page, query)));
    }

    /**
     * 导出轮询无效列表
     */
    @PostMapping("/list/exportInvalid")
    public void exportInvalid(HttpServletResponse response, @Valid PollingFriendQuery query) {
        List<PollingFriendInvalidVo> list = friendService.pollingInvalidList(null, query);
        ExcelConvertUtil.process(list);
        ExcelUtil<PollingFriendInvalidVo> util = new ExcelUtil<>(PollingFriendInvalidVo.class);
        util.exportExcel(response, list, "导出无效列表");
    }

    /**
     * 轮询审核
     */
    @GetMapping("/state")
    public AjaxResult state(@RequestParam(value = "reviewRemark", required = false) String reviewRemark,
            @RequestParam(value = "friendId") Long friendId,
            @RequestParam("state") Long state) {
        return success(friendService.pollingState(reviewRemark, friendId, state));
    }
    /**
     * 批量确认
     */
    @GetMapping("/batchConfirm")
    public AjaxResult batchConfirm(@RequestParam(value = "recordDate", required = false) LocalDate[] recordDate,
            @RequestParam(value = "businessType") String businessType
    ) {
        return success(friendService.pollingBatchConfirm(recordDate,businessType));
    }
    /**
     * 编辑
     */
//    @GetMapping("/edit")
//    public AjaxResult edit(@RequestBody CrmFriend friend) {
//        return success(friendService.pollingEdit(friend));
//    }

    /**
     * 轮询导入模板
     */
    @PostMapping("/import-template")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PollingFriendVo> util = new ExcelUtil<PollingFriendVo>(PollingFriendVo.class);
        util.importTemplateExcel(response, "轮询导入模板");
    }

    /**
     * 导入
     */
    @PostMapping("/importCustomer")
    public AjaxResult importCustomerCost(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<PollingFriendDto> util = new ExcelUtil<>(PollingFriendDto.class);
        List<PollingFriendDto> pollingFriendList = util.importExcel(file.getInputStream());
        if (CollUtil.isEmpty(pollingFriendList)) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        String message = friendService.pollingImport(pollingFriendList, updateSupport);
        return success(message);
    }

    /**
     * 聊天记录
     */
    @GetMapping("/chatList")
    public AjaxResult chatList(@RequestParam(value = "pollingApiUserId", required = false) String pollingApiUserId) {
        return success(friendService.chatList(pollingApiUserId));
    }

    /**
     * 轮询统计列表
     */
    @GetMapping("/statisticsList")
    public AjaxResult statisticsList(Page<PollingStatisticsVo> page, PollingFriendQuery query) {
        return success(page.setRecords(friendService.statisticsList(page, query)));
    }

    /**
     * 轮询统计列表导出
     */
    @PostMapping("/list/exportStatisticsList")
    public void exportStatisticsList(HttpServletResponse response, @Valid PollingFriendQuery query) {
        List<PollingStatisticsVo> list = friendService.statisticsList(null, query);
        ExcelConvertUtil.process(list);
        ExcelUtil<PollingStatisticsVo> util = new ExcelUtil<>(PollingStatisticsVo.class);
        util.exportExcel(response, list, "导出统计列表");
    }

    /**
     * 轮询查看备注
     */
    @GetMapping("/remarkList")
    public AjaxResult remarkList(@RequestParam(value = "friendId") Long friendId) {
        List<CrmFriendPollingReview> friendPollingReviewList = friendPollingReviewService.lambdaQuery()
                .eq(CrmFriendPollingReview::getFriendId, friendId)
                .orderByDesc(CrmFriendPollingReview::getCreateTime)
                .list();
        return success(friendPollingReviewList);
    }


}
