package com.yooa.crm.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.crm.api.domain.CrmCost;
import com.yooa.crm.api.domain.dto.FBCostEditDto;
import com.yooa.crm.api.domain.dto.FBCostSaveDto;
import com.yooa.crm.api.domain.dto.TKCostEditDto;
import com.yooa.crm.api.domain.query.CustomerCostQuery;
import com.yooa.crm.api.domain.vo.CostVo;
import com.yooa.crm.service.CostService;
import com.yooa.crm.service.impl.FriendConfirmImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 成本管理 - 控制层
 *
 * <AUTHOR>
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/cost")
public class CostController extends BaseController {

    private final CostService costService;
    private final FriendConfirmImpl friendConfirmImpl;

    /**
     * 获取成本统计 - 投手维度
     */
    @GetMapping("/statistics-pitcher")
    public AjaxResult statisticsByPitcher(CustomerCostQuery query) {
        return success(costService.statisticsByPitcher(query));
    }

    /**
     * 获取成本列表 - 投手维度
     */
    @GetMapping("/list-pitcher")
    public AjaxResult listByPitcher(Page<CostVo> page, CustomerCostQuery query) {
        return success(page.setRecords(costService.listByPitcher(page, query)));
    }

    /**
     * 导出成本列表 - 投手维度
     */
    @PostMapping("/export-pitcher")
    public void exportByPitcher(HttpServletResponse response, CustomerCostQuery query) {
        List<CostVo> customerCostList = costService.listByPitcher(null, query);
        for (CostVo costVo : customerCostList){
            if (StringUtils.isNotEmpty(costVo.getCurrency()) && "美金".equals(costVo.getCurrency())) {
                costVo.setCurrency("外币");
            }
        }
        ExcelUtil<CostVo> util = new ExcelUtil<>(CostVo.class);
        util.exportExcel(response, customerCostList, "成本列表");
    }

    /**
     * 获取成本统计 - 推广维度
     */
    @GetMapping("/statistics-extend")
    public AjaxResult statisticsByExtend(CustomerCostQuery query) {
        return success(costService.statisticsByExtend(query));
    }

    /**
     * 获取成本列表 - 推广维度
     */
    @GetMapping("/list-extend")
    public AjaxResult listByExtend(Page<CostVo> page, CustomerCostQuery query) {
        return success(page.setRecords(costService.listByExtend(page, query)));
    }

    /**
     * 导出成本列表 - 推广维度
     */
    @PostMapping("/export-extend")
    public void exportByExtend(HttpServletResponse response, CustomerCostQuery query) {
        List<CostVo> customerCostList = costService.listByExtend(null, query);
        ExcelUtil<CostVo> util = new ExcelUtil<>(CostVo.class);
        util.exportExcel(response, customerCostList, "成本列表");
    }

    /**
     * 获取成本详情
     */
    @GetMapping("/{costId}")
    public AjaxResult info(@PathVariable Long costId) {
        return success(costService.getById(costId));
    }

    /**
     * 新增成本
     */
    @Log(title = "成本管理", businessType = BusinessType.INSERT)
    @PostMapping("/fb")
    public AjaxResult add(@Valid @RequestBody List<FBCostSaveDto> fbCostSaveDtoList) {
        Set<String> checkSet = new HashSet<>();

        for (FBCostSaveDto fbCostSaveDto : fbCostSaveDtoList) {
            // 当前操作人即为投手
            fbCostSaveDto.setPitcherId(SecurityUtils.getUserId());

            if (!checkSet.add(fbCostSaveDto.getCostDate().toString()
                    + fbCostSaveDto.getPitcherId()
                    + fbCostSaveDto.getExtendId()
                    + fbCostSaveDto.getMainChannelId()
                    + fbCostSaveDto.getSubChannelId()
                    + fbCostSaveDto.getSex()
                    + fbCostSaveDto.getLanguage()
                    + fbCostSaveDto.getPublicType()) || ObjUtil.isNotNull(costService.checkCostUnique(fbCostSaveDto))) {
                return warn("成本数据重复：请检查[成本日期-投手-推广-渠道-性别-语言-对公类型]");
            }

            // 计算FB成本
            BigDecimal moneyUsd = calculationCostUsd(fbCostSaveDto.getFbClick(), fbCostSaveDto.getFbClickPrice());
            fbCostSaveDto.setMoneyUsd(moneyUsd);
            fbCostSaveDto.setMoneyCny(calculationCostCny(moneyUsd, fbCostSaveDto.getExchangeRate()));
        }

        // 转换FB实体
        List<CrmCost> fbCostList = BeanUtil.copyToList(fbCostSaveDtoList, CrmCost.class);
        return success(costService.saveBatch(fbCostList));
    }

    /**
     * TK修改成本 - 只允许修改好友单价
     */
    @Valid
    @Log(title = "成本管理", businessType = BusinessType.UPDATE)
    @PutMapping("/tk")
    public AjaxResult editTk(@Valid @RequestBody TKCostEditDto tkCostEditDto) {
        CrmCost cost = costService.checkCostUnique(tkCostEditDto);
        if (ObjUtil.isNotNull(cost) && !tkCostEditDto.getCostId().equals(cost.getCostId())) {
            return warn("成本数据重复：请检查[成本日期-投手-推广-渠道-性别-语言-对公类型]");
        }
        // 计算TK成本
        BigDecimal moneyUsd = calculationCostUsd(tkCostEditDto.getTkFriend(), tkCostEditDto.getTkFriendPrice());
        tkCostEditDto.setMoneyUsd(moneyUsd);
        tkCostEditDto.setMoneyCny(calculationCostCny(moneyUsd, tkCostEditDto.getExchangeRate()));
        return success(costService.updateById(tkCostEditDto));
    }

    /**
     * FB修改成本
     */
    @Log(title = "成本管理", businessType = BusinessType.UPDATE)
    @PutMapping("/fb")
    public AjaxResult edit(@Valid @RequestBody FBCostEditDto fbCostEditDto) {
        CrmCost cost = costService.checkCostUnique(fbCostEditDto);
        if (ObjUtil.isNotNull(cost) && !fbCostEditDto.getCostId().equals(cost.getCostId())) {
            return warn("成本数据重复：请检查[成本日期-投手-推广-渠道-性别-语言-对公类型]");
        }
        // 计算FB成本
        BigDecimal moneyUsd = calculationCostUsd(fbCostEditDto.getFbClick(), fbCostEditDto.getFbClickPrice());
        fbCostEditDto.setMoneyUsd(moneyUsd);
        fbCostEditDto.setMoneyCny(calculationCostCny(moneyUsd, fbCostEditDto.getExchangeRate()));
        return success(costService.updateById(fbCostEditDto));
    }

    /**
     * 删除成本
     */
    @Log(title = "成本管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{costId}")
    public AjaxResult remove(@PathVariable Long costId) {
        CrmCost cost = costService.getById(costId);
        // 删除成本需要去验证好友数确认是否有确认的数据，有就不允许删除
        if (StrUtil.isNotBlank(cost.getCostType()) && "1".equals(cost.getCostType())) {
            boolean unique = friendConfirmImpl.checkFriendConfirmUnique(cost.getCostDate(), cost.getPitcherId(),
                    cost.getExtendId(), cost.getPublicType(),
                    cost.getMainChannelId(), cost.getSubChannelId(),
                    cost.getSex(), cost.getLanguage());
            if (!unique) {
                return warn("该成本数据已被确认，无法删除");
            }
        }
        return success(costService.removeById(costId));
    }

    /**
     * 计算成本金额（美元）
     *
     * @param quantity 数量（TK好友数、FB点击数）
     * @param price    单价（TK好友单价、FB点击单价）
     * @return 成本金额（美元）
     */
    private BigDecimal calculationCostUsd(Integer quantity, BigDecimal price) {
        return price.multiply(new BigDecimal(quantity)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算成本金额（人民币）
     *
     * @param costUsd      成本金额（美元）
     * @param exchangeRate 汇率
     * @return 成本金额（人民币）
     */
    private BigDecimal calculationCostCny(BigDecimal costUsd, BigDecimal exchangeRate) {
        return costUsd.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
    }

}
