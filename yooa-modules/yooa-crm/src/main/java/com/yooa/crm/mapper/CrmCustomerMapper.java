package com.yooa.crm.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomer;
import com.yooa.crm.api.domain.query.CustomerDataTotalPerformanceQuery;
import com.yooa.crm.api.domain.query.CustomerQuery;
import com.yooa.crm.api.domain.vo.CustomerDataTotalPerformanceVo;
import com.yooa.crm.api.domain.vo.CustomerVo;
import com.yooa.crm.api.domain.vo.FriendCustomerDetailsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户 - 数据层
 */
public interface CrmCustomerMapper extends BaseMapper<CrmCustomer> {

    /**
     * 获取PD客户列表(推广)
     */
    List<CrmCustomer> selectCustomerList(Page<CrmCustomer> page, @Param("query") CustomerQuery query);

    /**
     * 获取PD客户列表(vip板块)
     */
    List<CrmCustomer> selectCustomerVipList(Page<CrmCustomer> page, @Param("query") CustomerQuery query);

    FriendCustomerDetailsVo selFriendCustomerVo(@Param("id") Long id);

    List<CustomerVo> selCustomerVoList(@Param("id") Long id);

    // 刷优质用户
    int updateQualityStatus();

    List<CrmCustomer> getCustomerNameByCustomerIds(@Param("ids") List<Long> customerIds);

    List<Long> getCustomerIdsByName(@Param("queryId") String queryId);

    /**
     * 数据统计 (客户数据)
     */
    List<CustomerDataTotalPerformanceVo> getCustomerDataTotalPerformance(Page page,@Param("query") CustomerDataTotalPerformanceQuery query);
}




