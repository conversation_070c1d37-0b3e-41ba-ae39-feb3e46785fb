package com.yooa.crm.listen.dts.business;

import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.crm.listen.dts.BaseKafkaHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;

/**
 *
 */
@Slf4j
public abstract class AbstractBaseBusiness extends BaseKafkaHandler {

    @Override
    @KafkaListener(topics = "#{__listener.getTopic()}", groupId = KafkaTopicConstants.GROUP_ID)
    public void commonListener(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        super.commonListener(consumerRecord, acknowledgment);
    }

}
