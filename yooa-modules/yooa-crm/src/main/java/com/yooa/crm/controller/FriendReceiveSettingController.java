package com.yooa.crm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.crm.api.domain.CrmFriendReceiveSetting;
import com.yooa.crm.api.domain.vo.ReceiveSettingVo;
import com.yooa.crm.service.FriendReceiveSettingService;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 好友领取设置 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/friend/receive/setting")
public class FriendReceiveSettingController extends BaseController {

    private final FriendReceiveSettingService friendReceiveSettingService;

    /**
     * 客户指标设置-设置领取数
     */
    @PostMapping("receiveSetting")
    public AjaxResult receiveSetting(@RequestBody List<CrmFriendReceiveSetting> crmFriendReceiveSettingList) {
        return AjaxResult.success(friendReceiveSettingService.receiveSetting(crmFriendReceiveSettingList));
    }

    /**
     * 客户指标设置-查询设置领取数
     */
    @GetMapping("selReceiveNumber")
    public AjaxResult selReceiveNumber(Page<?> page) {
        return AjaxResult.success(friendReceiveSettingService.selReceiveNumber(page));
    }

    /**
     * 客户指标设置-导出设置领取数
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response) {
        List<ReceiveSettingVo> receiveSettingVoList = friendReceiveSettingService.selReceiveNumber(new Page<>().setSize(-1)).getRecords();
        ExcelUtil<ReceiveSettingVo> util = new ExcelUtil<>(ReceiveSettingVo.class);
        util.exportExcel(response, receiveSettingVoList, "导出设置领取数");
    }

    /**
     * 客户指标设置-查询下级的领取详情(查看按钮)
     */
    @GetMapping("selReceiveNumberDetails")
    public AjaxResult selReceiveNumberDetails(Page<?> page, CommonalityDto dto) {
        return AjaxResult.success(friendReceiveSettingService.selReceiveNumberDetails(page, dto));
    }

}
