package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.crm.api.domain.CrmCustomerFollowRecord;
import com.yooa.crm.api.domain.query.CustomerFollowRecordQuery;
import com.yooa.crm.api.domain.vo.CustomerFollowRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户跟踪记录 - 数据层
 */
public interface CrmCustomerFollowRecordMapper extends BaseMapper<CrmCustomerFollowRecord> {

    List<CustomerFollowRecordVo> selectCustomerFollowRecordList(@Param("query") CustomerFollowRecordQuery query);

}




