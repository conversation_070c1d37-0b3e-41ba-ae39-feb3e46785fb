package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmCustomerFriend;

import java.util.List;

/**
 * 客户好友关联 - 服务层
 */
public interface CustomerFriendService extends IService<CrmCustomerFriend> {

    /**
     * 根据客户id获取其在绑的记录
     */
    CrmCustomerFriend getBindByCustomerId(Long customerId);

    /**
     * 根据客户id和推广id获取其在绑的记录
     */
    CrmCustomerFriend getBindByCustomerIdAndPyExtendId(Long customerId, Long pyExtendId);

    /**
     * 根据好友id获取其下在绑记录
     */
    List<CrmCustomerFriend> getBindByFriendId(Long friendId);
}
