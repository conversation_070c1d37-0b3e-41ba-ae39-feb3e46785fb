package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmCustomerFollowComment;
import com.yooa.crm.api.domain.vo.CustomerFollowCommentVo;
import com.yooa.crm.mapper.CrmCustomerFollowCommentMapper;
import com.yooa.crm.service.CustomerFollowCommentService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户跟踪评论 - 服务实现层
 */
@AllArgsConstructor
@Service
public class CustomerFollowCommentServiceImpl extends ServiceImpl<CrmCustomerFollowCommentMapper, CrmCustomerFollowComment> implements CustomerFollowCommentService {

    @Override
    public List<CustomerFollowCommentVo> list(Long followId) {
        return baseMapper.selectList(followId);
    }
}
