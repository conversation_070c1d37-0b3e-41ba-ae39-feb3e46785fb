package com.yooa.crm.listen.dts;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yooa.common.core.constant.KafkaBusinessTopicConstants;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.common.core.utils.ObjectConvertUtil;
import com.yooa.crm.api.domain.CrmCustomerJoinAnchor;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.service.CustomerJoinAnchorService;
import com.yooa.crm.service.CustomerOrderService;
import com.yooa.extend.api.RemoteVermicelliService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RedissonClient;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 * 充值表
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UsersChargeListener extends AbstractBaseListener {

    private final CustomerOrderService customerOrderService;

    private final RedissonClient redissonClient;

    private final RemoteUserService remoteUserService;

    private final CustomerJoinAnchorService customerJoinAnchorService;

    private final CrmCustomerFriendMapper customerFriendMapper;

    private final RemoteVermicelliService remoteVermicelliService;

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_USERS_CHARGE;
    }

    @Override
    public String getBusTopic() {
        return KafkaBusinessTopicConstants.BUS_CMF_USERS_CHARGE;
    }

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();

    }


    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of("status", "id", "orderno", "coin", "coin_give2", "type",
                "yuanbao", "yuanbao_give", "addtime", "uid", "admin_id", "serve_id", "trade_no", "trade_date_time");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap);
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    protected void handleBusinessLogic(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        String id = getAfterValueByFieldName("id", fieldMap);
        String orderNo = getAfterValueByFieldName("orderno", fieldMap);
        // 状态"0-未支付","1-已完成","2-用户退款"
        String orderStatus = getAfterValueByFieldName("status", fieldMap);
        // 钻石数
        String coin = getAfterValueByFieldName("coin", fieldMap);
        // 赠送金币数
        String coinGive = getAfterValueByFieldName("coin_give2", fieldMap);
        // 元宝数
        String yuanbao = getAfterValueByFieldName("yuanbao", fieldMap);
        // 赠送元宝数
        String yuanbaoGive = getAfterValueByFieldName("yuanbao_give", fieldMap);
        // 订单发起时间
        String addTime = getAfterValueByFieldName("addtime", fieldMap);
        // 客户id
        String customerId = getAfterValueByFieldName("uid", fieldMap);
        // 推广id
        String extendId = getAfterValueByFieldName("admin_id", fieldMap);
        // 客服id
        String serveId = getAfterValueByFieldName("serve_id", fieldMap);
        // Pd订单号
        String pdOrderNo = getAfterValueByFieldName("orderno", fieldMap);
        // Pd第三方订单号
        String pdThirdOrderNo = getAfterValueByFieldName("trade_no", fieldMap);
        // 订单交易成功时间
        String tradeDateTime = getAfterValueByFieldName("trade_date_time", fieldMap);
        // 支付方式
        String paymentType = getAfterValueByFieldName("type", fieldMap);

        // 业务处理
        handlerCdcCmfChargeBusinse(orderStatus, coin, coinGive, yuanbao, yuanbaoGive, orderNo, id,
                addTime, extendId, serveId, pdOrderNo, pdThirdOrderNo, customerId, tradeDateTime, paymentType);
    }

    /**
     * 状态"0-未支付","1-已完成","2-用户退款"
     * 只处理状态已完成的 1-已完成 2-用户退款
     */
    private void handlerCdcCmfChargeBusinse(String orderStatus, String coin, String coinGive, String yuanbao, String yuanbaoGive,
            String orderNo, String id, String addTime, String extendId, String serveId, String pdOrderNo,
            String pdThirdOrderNo, String customerId, String tradeDateTime, String paymentType) {
        if (StrUtil.isNotBlank(orderStatus) && StrUtil.equals(orderStatus, "1")) {
            // 获取订单金额
            BigDecimal orderMoney = getOrderMoney(coin, coinGive, yuanbao, yuanbaoGive);
            // crm_customer_order表关联cmf_users_charge表 对应关联字段为  crm.customer_order."order_no" = cmf_users_charge."orderno"+"_"+"id"
            String customerOrderNo = StrUtil.format("{}_{}", orderNo, id);
            // 新增或更新数据
            saveOrUpdateCrmCustomerOrder(id, addTime, extendId, serveId, pdOrderNo,
                    pdThirdOrderNo, customerId, tradeDateTime, customerOrderNo, orderMoney, paymentType);
            super.sendBusTopic = true;
        }
        // 退款
        if (StrUtil.isNotBlank(orderStatus) && StrUtil.equals(orderStatus, "2")) {
            super.sendBusTopic = customerOrderService.update(Wrappers.<CrmCustomerOrder>lambdaUpdate()
                    .eq(CrmCustomerOrder::getOrderId, id)
                    .set(CrmCustomerOrder::getOrderStatus, 2)
            );
            if (!sendBusTopic) {
                throw new ServiceException("UsersChargeListener update faild");
            }
        }
    }

    /**
     * 订单金额
     * 订单金额计算公式 ROUND((IFNULL(coin,0) + IFNULL(coin_give2,0)) / 100,2) + (IFNULL(yuanbao,0) + IFNULL(yuanbao_give,0)) AS order_money
     * ((钻石数 + 赠送钻石数) / 100) + (元宝数 + 赠送元宝数)
     */
    private BigDecimal getOrderMoney(String coin, String coinGive, String yuanbao, String yuanbaoGive) {
        // 钻石数
        coin = StrUtil.isNotBlank(coin) ? coin : "0";
        // 赠送钻石数
        coinGive = StrUtil.isNotBlank(coinGive) ? coinGive : "0";
        // 元宝数
        yuanbao = StrUtil.isNotBlank(yuanbao) ? yuanbao : "0";
        // 赠送元宝数
        yuanbaoGive = StrUtil.isNotBlank(yuanbaoGive) ? yuanbaoGive : "0";
        return NumberUtil.add(coin, coinGive)
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)
                .add(NumberUtil.add(yuanbao, yuanbaoGive));
    }

    /**
     * 组装crmCustomerOrder数据
     */
    private void saveOrUpdateCrmCustomerOrder(String id, String addTime, String extendId, String serveId, String pdOrderNo, String pdThirdOrderNo,
            String customerId, String tradeDateTime, String customerOrderNo, BigDecimal orderMoney, String paymentType) {
        CrmCustomerOrder crmCustomerOrder = new CrmCustomerOrder();
        crmCustomerOrder.setOrderId(convertToLong(id));

        // 订单状态(1:已完成、2:已退款、3:特殊退款)
        crmCustomerOrder.setOrderStatus(1);
        // 订单金额
        crmCustomerOrder.setOrderMoney(orderMoney);
        // 订单编号
        crmCustomerOrder.setOrderNo(customerOrderNo);
        // 订单发起时间
        crmCustomerOrder.setOrderTime(LocalDateUtil.epochSecondToLocalDateTime(addTime));
        // 推广id
        crmCustomerOrder.setPyExtendId(convertToLong(extendId));
        // 客服id
        crmCustomerOrder.setPyServeId(convertToLong(serveId));
        // pd订单号
        crmCustomerOrder.setPdOrderNo(pdOrderNo);
        // pd第三方订单号
        crmCustomerOrder.setPdThirdOrderNo(pdThirdOrderNo);
        // 客户id
        crmCustomerOrder.setCustomerId(convertToLong(customerId));
        // 订单完成时间
        crmCustomerOrder.setCompleteTime(StrUtil.isNotBlank(tradeDateTime) && ObjectUtil.notEqual(tradeDateTime, "0")
                ? LocalDateUtil.epochSecondToLocalDateTime(tradeDateTime)
                : LocalDateUtil.epochSecondToLocalDateTime(addTime));
        crmCustomerOrder.setPaymentType(StrUtil.isNotBlank(paymentType) ? Convert.toInt(paymentType) : null);

        // 推广id 客服id 请求集
        List<Long> pdUserIds = Stream.of(extendId, serveId)
                .filter(ids -> StrUtil.isNotBlank(ids) && !StrUtil.equals(ids, "0"))
                .map(ObjectConvertUtil::convertToLong)
                .toList();

        if (CollectionUtil.isNotEmpty(pdUserIds)) {
            // 获取用户列表
            List<SysUserVo> userList = remoteUserService.getUserList(UserQuery.builder().pdUserId(pdUserIds).build(), SecurityConstants.INNER).getData();
            if (CollectionUtil.isNotEmpty(userList)) {
                if (userList.size() > 2) {
                    throw new ServiceException("UsersChargeListener remoteUserService.getUserList result size > 2");
                }

                // 合并数据流
                Map<Long, SysUserVo> userMap = userList
                        .stream()
                        .flatMap(user -> user.getPdUserId()
                                .stream()
                                .map(pdId -> Pair.of(pdId, user)))
                        .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
                // 填充数据
                setupUserRelation(crmCustomerOrder, userMap, convertToLong(extendId),
                        CrmCustomerOrder::setExtendId, CrmCustomerOrder::setExtendDeptId);

                setupUserRelation(crmCustomerOrder, userMap, convertToLong(serveId),
                        CrmCustomerOrder::setServeId, CrmCustomerOrder::setServeDeptId);
            }

            // 查询一交表中该客户已交接并且订单完成时间在交接时间后的90天内的数据 塞首充时间字段
            List<CrmCustomerJoinAnchor> customerJoinAnchorList = customerJoinAnchorService.getCustomerJoinAnchorByCustomerId(convertToLong(customerId), LocalDateUtil.epochSecondToLocalDateTime(addTime));
            if (CollectionUtil.isNotEmpty(customerJoinAnchorList)) {
                customerJoinAnchorList.forEach(customerJoinAnchor -> {
                    customerJoinAnchor.setFirstChargeDate(LocalDate.now());
                });

                /**原运营首充逻辑**/
//                customerJoinAnchorList.stream().map(CrmCustomerJoinAnchor::getCustomerId).distinct().forEach(cId -> {
//                    CrmCustomerJoinAnchor anchor = customerJoinAnchorList.stream().filter(c -> c.getCustomerId().equals(cId)).findFirst().orElse(null);
//
//                    if (ObjectUtil.isNotNull(anchor)) {
//                        List<CrmCustomerFriend> customerFriendList = customerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
//                                .eq(CrmCustomerFriend::getCustomerId, anchor.getCustomerId())
//                                .eq(CrmCustomerFriend::getPyExtendId, anchor.getExtendId())
//                                .eq(CrmCustomerFriend::getStatus, 0)
//                                .le(CrmCustomerFriend::getBeginTime, anchor.getReceiveTime())
//                                .apply("(end_time IS NULL or end_time >= {0})", anchor.getReceiveTime()));
//
//                        if (CollUtil.isNotEmpty(customerFriendList)) {
//
//                            SysUserVo operateUser = new SysUserVo();
//                            List<SysUserVo> sysUserVoList = remoteUserService.getUserList(
//                                    UserQuery.builder()
//                                            .pdUserId(CollUtil.newArrayList(anchor.getOperateId()))
//                                            .build(), SecurityConstants.INNER).getData();
//
//                            if (CollectionUtil.isNotEmpty(sysUserVoList)) {
//                                operateUser = sysUserVoList.get(0);
//                            }
//
//                            remoteVermicelliService.addOperateVermicelli(OperateVermicelli.builder()
//                                    .fansType(7)        // 首充
//                                    .friendId(customerFriendList.get(0).getFriendId())
//                                    .customerIds(anchor.getCustomerId().toString())
//                                    .anchorId(anchor.getAnchorId())
//                                    .recordDate(crmCustomerOrder.getOrderTime().toLocalDate())
//                                    .createTime(LocalDateTime.now())
//                                    .createBy(1L)
//                                    .remark("充值表产生粉丝登记")
//                                    .pyOperateId(anchor.getOperateId())
//                                    .operateId(operateUser.getUserId())
//                                    .operateDeptId(operateUser.getDeptId())
//                                    .build(), SecurityConstants.INNER);         // 新增运营的首充粉丝登记
//                        }
//                    }
//                });
            }

        }
        customerOrderService.saveOrUpdate(crmCustomerOrder);

    }

    /**
     * 设置属性
     */
    private void setupUserRelation(CrmCustomerOrder order,
            Map<Long, SysUserVo> userMap,
            Long pdUserId,
            BiConsumer<CrmCustomerOrder, Long> setId,
            BiConsumer<CrmCustomerOrder, Long> setDeptId) {
        if (pdUserId == null || pdUserId == 0) {
            return;
        }

        SysUserVo user = userMap.get(pdUserId);
        if (user != null) {
            setId.accept(order, user.getUserId());
            setDeptId.accept(order, user.getDeptId());
        }
    }

}
