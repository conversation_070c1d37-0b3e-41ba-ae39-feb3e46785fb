package com.yooa.crm.listen.dts;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.crm.api.domain.CrmAnchor;
import com.yooa.crm.api.domain.CrmAnchorAccountMapping;
import com.yooa.crm.api.domain.CrmAnchorInfo;
import com.yooa.crm.mapper.CrmAnchorAccountMappingMapper;
import com.yooa.crm.mapper.CrmAnchorInfoMapper;
import com.yooa.crm.mapper.CrmAnchorMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.yooa.common.core.utils.ObjectConvertUtil.*;

/**
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UsersAuthListener extends AbstractBaseListener {

    private final CrmAnchorMapper anchorMapper;

    private final CrmAnchorInfoMapper anchorInfoMapper;

    private final CrmAnchorAccountMappingMapper anchorAccountMappingMapper;

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();
    }

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_USERS_AUTH;
    }

    @Override
    protected List<String> getFilterList() {
        return List.of("uid", "live_status", "status");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        updateAnchor(fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    public void updateAnchor(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        Long anchorId = convertToLong(getAfterValueByFieldName("uid", fieldMap));
        Long liveStatus = convertToLong(getAfterValueByFieldName("live_status", fieldMap));
        Long status = convertToLong(getAfterValueByFieldName("status", fieldMap));

        // 停播将主播状态改成已解约 更新停播时间
        if(liveStatus == 0){
            CrmAnchorAccountMapping crmAnchorAccountMapping = anchorAccountMappingMapper.selectOne(new LambdaQueryWrapper<CrmAnchorAccountMapping>()
                    .eq(CrmAnchorAccountMapping::getAccountId, anchorId));
            if(crmAnchorAccountMapping != null){
                new LambdaUpdateChainWrapper<>(anchorInfoMapper)
                        .eq(CrmAnchorInfo::getAnchorId, crmAnchorAccountMapping.getAnchorId())
                        .set(CrmAnchorInfo::getAnchorStatus, "4")
                        .set(CrmAnchorInfo::getStopLiveTime, LocalDateTime.now())
                        .update(new CrmAnchorInfo());
            }

        }

        // 待停播 更新状态为待停播
        if(status == 2){
            CrmAnchorAccountMapping crmAnchorAccountMapping = anchorAccountMappingMapper.selectOne(new LambdaQueryWrapper<CrmAnchorAccountMapping>()
                    .eq(CrmAnchorAccountMapping::getAccountId, anchorId));
            if(crmAnchorAccountMapping != null){
                new LambdaUpdateChainWrapper<>(anchorInfoMapper)
                        .eq(CrmAnchorInfo::getAnchorId, crmAnchorAccountMapping.getAnchorId())
                        .set(CrmAnchorInfo::getLiveStatus, "5")
                        .update(new CrmAnchorInfo());
            }

        }

        sendBusTopic = new LambdaUpdateChainWrapper<>(anchorMapper)
                .eq(CrmAnchor::getAnchorId, anchorId)
                .set(CrmAnchor::getLiveRole, liveStatus)
                .update(new CrmAnchor());
        if (!sendBusTopic) {
            throw new ServiceException("UsersAuthListener update failed");
        }
    }
}
