package com.yooa.external.dingtalk.controller;

import cn.hutool.core.bean.BeanUtil;
import com.aliyun.dingtalkhrm_1_0.Client;
import com.aliyun.dingtalkhrm_1_0.models.*;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiSmartworkHrmEmployeeQueryonjobRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiSmartworkHrmEmployeeQueryonjobResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.yooa.common.core.domain.R;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.external.api.response.DingTalkUserLeaveInfoRes;
import com.yooa.external.api.response.DingTalkUserLeaveListRes;
import com.yooa.external.api.response.DingTalkUserNormalListRes;
import com.yooa.external.api.response.DingTalkUserRes;
import com.yooa.external.dingtalk.service.DingTalkAccessTokenService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 钉钉用户 - 控制层
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/ding-talk/user")
public class DingTalkUserController {

    private final DingTalkAccessTokenService dingTalkAccessTokenService;

    /**
     * 获取钉钉用户详情
     *
     * @param userId 钉钉用户id
     * @return 钉钉用户详情
     */
    @SneakyThrows
    @InnerAuth
    @GetMapping("/{userId}")
    public R<DingTalkUserRes> getByUserId(@PathVariable String userId) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        req.setLanguage("zh_CN");

        OapiV2UserGetResponse res = client.execute(req, dingTalkAccessTokenService.getAccessToken());

        DingTalkUserRes dingTalkUserRes = new DingTalkUserRes();
        BeanUtil.copyProperties(res.getResult(), dingTalkUserRes);
        return R.ok(dingTalkUserRes);
    }

    /**
     * 获取钉钉部门下的用户详情列表
     *
     * @param deptId 钉钉部门id
     * @param cursor 分页游标
     * @param size   分页大小
     * @return 钉钉用户详情列表
     */
    @SneakyThrows
    @InnerAuth
    @GetMapping("/list/{deptId}")
    public R<List<DingTalkUserRes>> listByDeptId(@PathVariable Long deptId,
            @RequestParam(defaultValue = "0") Long cursor,
            @RequestParam(defaultValue = "100") Long size) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(deptId);
        req.setCursor(cursor);
        req.setSize(size);
        req.setOrderField("modify_desc");
        req.setContainAccessLimit(false);
        req.setLanguage("zh_CN");

        OapiV2UserListResponse res = client.execute(req, dingTalkAccessTokenService.getAccessToken());

        List<DingTalkUserRes> dingTalkUserRes = BeanUtil.copyToList(res.getResult().getList(), DingTalkUserRes.class);
        return R.ok(dingTalkUserRes);
    }

    /**
     * 获取钉钉在职员工列表
     *
     * @param status 在职员工状态筛选，可以查询多个状态。不同状态之间使用英文逗号分隔
     *               <p>
     *               2：试用期
     *               <p>
     *               3：正式
     *               <p>
     *               5：待离职
     *               <p>
     *               -1：无状态
     * @param cursor 分页游标，从0开始，根据返回结果里的next_cursor是否为空来判断是否还有下一页，且再次调用时offset设置成next_cursor的值
     * @param size   分页大小，最大50
     */
    @SneakyThrows
    @InnerAuth
    @GetMapping("/normal-list")
    public R<DingTalkUserNormalListRes> listByNormal(@RequestParam(defaultValue = "3") String status,
            @RequestParam(defaultValue = "0") Long cursor,
            @RequestParam(defaultValue = "50") Long size) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/queryonjob");
        OapiSmartworkHrmEmployeeQueryonjobRequest req = new OapiSmartworkHrmEmployeeQueryonjobRequest();
        req.setStatusList(status);
        req.setOffset(cursor);
        req.setSize(size);

        OapiSmartworkHrmEmployeeQueryonjobResponse res = client.execute(req, dingTalkAccessTokenService.getAccessToken());

        DingTalkUserNormalListRes dingTalkUserNormalListRes = new DingTalkUserNormalListRes();
        BeanUtil.copyProperties(res.getResult(), dingTalkUserNormalListRes);
        return R.ok(dingTalkUserNormalListRes);
    }

    /**
     * 获取钉钉离职员工列表
     *
     * @param cursor 分页查询的游标，如果是首次查询，该参数传0或者不传，如果是非首次查询，该参数传上次调用时返回的nextToken
     * @param size   每页条目数，默认值30，最大值50
     */
    @SneakyThrows
    @InnerAuth
    @GetMapping("/leave-list")
    public R<DingTalkUserLeaveListRes> listByLeave(@RequestParam(defaultValue = "0") Long cursor,
            @RequestParam(defaultValue = "50") Integer size) {
        Client client = createClient();
        QueryDismissionStaffIdListHeaders headers = new QueryDismissionStaffIdListHeaders();
        headers.xAcsDingtalkAccessToken = dingTalkAccessTokenService.getAccessToken();
        QueryDismissionStaffIdListRequest req = new QueryDismissionStaffIdListRequest()
                .setNextToken(cursor)
                .setMaxResults(size);

        QueryDismissionStaffIdListResponse res = client.queryDismissionStaffIdListWithOptions(req, headers, new RuntimeOptions());

        DingTalkUserLeaveListRes dingTalkUserLeaveListRes = new DingTalkUserLeaveListRes();
        BeanUtil.copyProperties(res.getBody(), dingTalkUserLeaveListRes);
        return R.ok(dingTalkUserLeaveListRes);
    }

    /**
     * 获取钉钉离职员工信息
     *
     * @param userId 钉钉用户id
     */
    @SneakyThrows
    @InnerAuth
    @GetMapping("/leave-info/{userId}")
    public R<DingTalkUserLeaveInfoRes> getLeaveInfo(@PathVariable String userId) {
        Client client = createClient();
        QueryHrmEmployeeDismissionInfoHeaders headers = new QueryHrmEmployeeDismissionInfoHeaders();
        headers.xAcsDingtalkAccessToken = dingTalkAccessTokenService.getAccessToken();
        QueryHrmEmployeeDismissionInfoRequest req = new QueryHrmEmployeeDismissionInfoRequest()
                .setUserIdList(Collections.singletonList(userId));

        QueryHrmEmployeeDismissionInfoResponse res = client.queryHrmEmployeeDismissionInfoWithOptions(req, headers, new RuntimeOptions());

        DingTalkUserLeaveInfoRes dingTalkUserLeaveInfoRes = new DingTalkUserLeaveInfoRes();
        BeanUtil.copyProperties(res.getBody().getResult().get(0), dingTalkUserLeaveInfoRes);
        return R.ok(dingTalkUserLeaveInfoRes);
    }

    public Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }
}
