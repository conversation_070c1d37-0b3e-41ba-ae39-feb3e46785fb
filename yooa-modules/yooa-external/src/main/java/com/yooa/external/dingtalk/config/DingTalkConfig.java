package com.yooa.external.dingtalk.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 钉钉配置文件
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {

    private boolean enabled;

    private String appId;

    private Long agentId;

    private String clientId;

    private String clientSecret;
}
