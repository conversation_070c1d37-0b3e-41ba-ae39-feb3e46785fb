package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.common.core.domain.R;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.domain.vo.DeptAndUserVo;
import com.yooa.system.domain.vo.TreeSelect;

import java.util.List;

/**
 * 部门管理 - 服务层
 */
public interface DeptService extends IService<SysDept> {

    /**
     * 查询部门管理数据
     */
    public List<SysDept> selectDeptList(DeptQuery query);

    /**
     * 获取部门详情数据
     */
    public SysDept getDeptById(Long deptId);

    /**
     * 查询部门树结构信息
     */
    public List<TreeSelect> selectAuthDeptTreeList(DeptQuery query);

    /**
     * 查询部门树结构信息
     */
    public List<TreeSelect> selectDeptTreeList(DeptQuery query);

    /**
     * 获取部门及他的子部门树
     */
    public List<TreeSelect> selectDeptTreeChild(DeptQuery query);

    /**
     * 构建前端所需要树结构
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据角色ID查询部门树信息
     */
    public List<Long> selectDeptListByRoleId(Long roleId);

    /**
     * 根据角色ID查询父部门为世纪征途的部门
     */
    public List<Long> selectSJListByRoleId(Long roleId);

    /**
     * 根据ID查询所有子部门（正常状态）
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     */
    public boolean checkDeptNameUnique(SysDept dept);

    /**
     * 校验部门是否有数据权限
     */
    public void checkDeptDataScope(Long deptId);

    /**
     * 新增保存部门信息
     */
    public int insertDept(SysDept dept);

    /**
     * 修改保存部门信息
     */
    public int updateDept(SysDept dept);

    /**
     * 根据部门条件查询部门信息详情(feign调用)
     */
    public R<List<SysDept>> getDeptList(DeptQuery query);

    public List<SysDept> getDeptList(Page<SysDept> page, DeptQuery query);

    public DeptAndUserVo getDeptTreeAndUserVo();
}
