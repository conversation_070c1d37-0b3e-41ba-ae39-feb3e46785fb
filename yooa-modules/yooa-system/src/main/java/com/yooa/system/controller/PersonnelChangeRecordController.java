package com.yooa.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.system.api.domain.SysInterview;
import com.yooa.system.api.domain.SysRoster;
import com.yooa.system.api.domain.query.PersonnelChangeRecordQuery;
import com.yooa.system.service.InterviewService;
import com.yooa.system.service.PersonnelChangeRecordService;
import com.yooa.system.service.RosterService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 人员变动记录 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/change-record")
public class PersonnelChangeRecordController extends BaseController {

    private final PersonnelChangeRecordService personnelChangeRecordService;
    private final RosterService rosterService;
    private final InterviewService interviewService;

    @GetMapping("/interview/list")
    public AjaxResult listByInterview(PersonnelChangeRecordQuery query) {
        return success(personnelChangeRecordService.getPersonnelChangeRecordList(query));
    }

    @GetMapping("/roster/list")
    public AjaxResult listByRosterId(PersonnelChangeRecordQuery query) {
        SysRoster roster = rosterService.getById(query.getRosterId());
        if (ObjUtil.isNotNull(roster) && StrUtil.isNotBlank(roster.getIdCardNumber())) {
            List<SysInterview> interviews = interviewService.listByIdCardNumber(roster.getIdCardNumber());
            if (CollUtil.isNotEmpty(interviews)) {
                query.setInterViewIds(interviews.stream().map(SysInterview::getInterviewId).toList());
                return success(personnelChangeRecordService.getPersonnelChangeRecordList(query));
            }
        }
        return success(Collections.emptyList());
    }
}
