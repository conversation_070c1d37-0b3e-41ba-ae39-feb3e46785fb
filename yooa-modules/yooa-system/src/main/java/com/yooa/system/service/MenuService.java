package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysMenu;
import com.yooa.system.api.domain.query.MenuQuery;
import com.yooa.system.domain.vo.RouterVo;
import com.yooa.system.domain.vo.TreeSelect;

import java.util.List;
import java.util.Set;

/**
 * 菜单 - 业务层
 */
public interface MenuService extends IService<SysMenu> {
    /**
     * 根据用户查询系统菜单列表
     */
    public List<SysMenu> selectMenuList(Long userId);

    /**
     * 根据用户查询系统菜单列表
     */
    public List<SysMenu> selectMenuList(MenuQuery query, Long userId);

    /**
     * 根据用户ID查询权限
     */
    public Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据角色ID查询权限
     */
    public Set<String> selectMenuPermsByRoleId(Long roleId);

    /**
     * 根据用户ID查询菜单树信息
     */
    public List<SysMenu> selectMenuTreeByUserId(Long userId);

    /**
     * 根据角色ID查询菜单树信息
     */
    public List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端路由所需要的菜单
     */
    public List<RouterVo> buildMenus(List<SysMenu> menus);

    /**
     * 构建前端所需要树结构
     */
    public List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     */
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus);

    /**
     * 是否存在菜单子节点
     */
    public boolean hasChildByMenuId(Long menuId);

    /**
     * 查询菜单是否存在角色
     */
    public boolean checkMenuExistRole(Long menuId);

    /**
     * 校验菜单名称是否唯一
     */
    public boolean checkMenuNameUnique(SysMenu menu);
}
