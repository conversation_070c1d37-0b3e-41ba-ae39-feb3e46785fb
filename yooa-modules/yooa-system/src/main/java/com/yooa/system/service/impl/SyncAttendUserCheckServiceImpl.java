package com.yooa.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.DateUtil;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.external.api.RemoteDingTalkAttendanceService;
import com.yooa.external.api.request.DingTalkAttendUserCheckRequest;
import com.yooa.external.api.response.DingTalkAttendUserCheckRes;
import com.yooa.system.api.domain.SysAttendRecord;
import com.yooa.system.api.domain.vo.AttendUserVo;
import com.yooa.system.domain.dto.UserAttendCheckQuery;
import com.yooa.system.domain.vo.AttendCheckVo;
import com.yooa.system.domain.vo.AttendUserCheckVo;
import com.yooa.system.domain.vo.AttendScheduleVo;
import com.yooa.system.mapper.SysAttendRecordMapper;
import com.yooa.system.mapper.SysAttendScheduleMapper;
import com.yooa.system.mapper.SysUserMapper;
import com.yooa.system.service.SyncAttendUserCheckService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
public class SyncAttendUserCheckServiceImpl extends ServiceImpl<SysAttendRecordMapper, SysAttendRecord> implements SyncAttendUserCheckService {


    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(2, 6, 60, TimeUnit.MINUTES, new ArrayBlockingQueue(200), new ThreadPoolExecutor.CallerRunsPolicy());
    @Autowired
    private RemoteDingTalkAttendanceService remoteDingTalkAttendanceService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysAttendScheduleMapper sysAttendScheduleMapper;

    @Override
    public void syncAttendUserCheck() {
        // 获取系统所有用户
        List<AttendUserVo> attendUserVoList = sysUserMapper.queryDingUser();
        if (attendUserVoList == null || attendUserVoList.size() == 0) {
            return;
        }
        // 每次拉取T-1的数据
        Date endDate = DateUtil.getDayStart(new Date());
        Date startDate = DateUtil.getAfterDays(endDate, -3);
        // 清除当前时间范围的数据
        baseMapper.deleteByTime(startDate, endDate);
        // baseMapper.deleteByTime(DateUtil.stringConvertDate("2025-05-05 00:00:00",DateUtil.y_M_d_H_m_s), DateUtil.stringConvertDate("2025-05-11 00:00:00",DateUtil.y_M_d_H_m_s));
        // 循环处理获取数据
        List<List<AttendUserVo>> attendUserList = ListUtil.partition(attendUserVoList, 50);
        for (List<AttendUserVo> userVoList : attendUserList) {
            CompletableFuture.runAsync(() ->
                    // detailData(userVoList, "2025-04-25 00:00:00", "2025-04-30 00:00:00"), executor);
                    detailData(userVoList, DateUtil.dateConvertString(startDate, DateUtil.y_M_d_H_m_s), DateUtil.dateConvertString(endDate, DateUtil.y_M_d_H_m_s)), executor);
        }
    }

    @Override
    public List<AttendCheckVo> queryAttendList(UserAttendCheckQuery query) {
        if (StringUtils.isBlank(query.getDate())) {
            throw new ServiceException("查询月份不能为空");
        }
        Long oaUserId = SecurityUtils.getUserId();
        Date startDate = DateUtil.stringConvertDate(query.getDate() + "-01 00:00:00", DateUtil.y_M_d_H_m_s);
        Date endDate = DateUtil.getMonthFinally(startDate);
        List<AttendScheduleVo> attendScheduleVoList = sysAttendScheduleMapper.queryScheduleByUserAndDate(startDate, endDate, oaUserId);
        if (attendScheduleVoList == null || attendScheduleVoList.size() == 0) {
            return new ArrayList<>();
        }
        List<SysAttendRecord> sysAttendRecordList = baseMapper.queryRecordByUserAndDate(startDate, endDate, oaUserId);
        return detailAttendData(attendScheduleVoList, sysAttendRecordList);
    }


    private List<AttendCheckVo> detailAttendData(List<AttendScheduleVo> attendScheduleVoList, List<SysAttendRecord> sysAttendRecordList) {
        String nowDate = DateUtil.dateConvertString(new Date(), DateUtil.y_M_d);
        Map<String, AttendCheckVo> map = new HashMap<>();
        for (AttendScheduleVo attendScheduleVo : attendScheduleVoList) {
            AttendCheckVo attendCheckVo = new AttendCheckVo();
            attendCheckVo.setDate(DateUtil.dateConvertString(attendScheduleVo.getPlanDate(), DateUtil.y_M_d));
            attendCheckVo.setClassName(attendScheduleVo.getClassName());
            attendCheckVo.setOnTime(attendScheduleVo.getOnTime());
            attendCheckVo.setOffTime(attendScheduleVo.getOffTime());

            // 是否当天
            /*if (nowDate.equals(attendCheckVo.getDate())) {
                attendCheckVo.setWhtherNowDay("0");
            }else {
                attendCheckVo.setWhtherNowDay("1");
            }*/
            // 是否排班
            if (StringUtils.isBlank(attendScheduleVo.getClassId())) {
                attendCheckVo.setWetherSchedule("1");
            }
            else {
                attendCheckVo.setWetherSchedule("0");
            }
            map.put(attendCheckVo.getDate(), attendCheckVo);
        }
        if (sysAttendRecordList == null || sysAttendRecordList.size() == 0) {
            return map.values().stream().collect(Collectors.toList());
        }
        for (SysAttendRecord sysAttendRecord : sysAttendRecordList) {
            AttendCheckVo attendCheckVo = map.get(DateUtil.dateConvertString(sysAttendRecord.getWorkDate(), DateUtil.y_M_d));
            if (!"Normal".equals(sysAttendRecord.getTimeResult()) && !"SYSTEM".equals(sysAttendRecord.getSourceType())) {
                AttendUserCheckVo attendUserCheckVo = new AttendUserCheckVo();
                if (sysAttendRecord.getUserCheckTime() != null) {
                    attendUserCheckVo.setCheckTime(DateUtil.dateConvertString(sysAttendRecord.getUserCheckTime(), DateUtil.y_M_d_H_m_s));
                }
                attendUserCheckVo.setIllegalType(sysAttendRecord.getTimeResult());
                attendUserCheckVo.setCheckType(sysAttendRecord.getCheckType());
                List<AttendUserCheckVo> checkVoAttendIllegalVoList = attendCheckVo.getAttendIllegalVoList();
                if (checkVoAttendIllegalVoList == null || checkVoAttendIllegalVoList.size() == 0) {
                    checkVoAttendIllegalVoList = new ArrayList<>();
                }
                checkVoAttendIllegalVoList.add(attendUserCheckVo);
                attendCheckVo.setAttendIllegalVoList(checkVoAttendIllegalVoList);
                map.put(DateUtil.dateConvertString(sysAttendRecord.getWorkDate(), DateUtil.y_M_d), attendCheckVo);
                continue;
            }
            List<AttendUserCheckVo> attendUserCheckVoList = attendCheckVo.getAttendUserCheckVoList();
            if (attendUserCheckVoList == null || attendUserCheckVoList.size() == 0) {
                attendUserCheckVoList = new ArrayList<>();
            }
            AttendUserCheckVo attendUserCheckVo = new AttendUserCheckVo();
            attendUserCheckVo.setCheckType(sysAttendRecord.getCheckType());
            if (sysAttendRecord.getUserCheckTime() != null) {
                attendUserCheckVo.setCheckTime(DateUtil.dateConvertString(sysAttendRecord.getUserCheckTime(), DateUtil.y_M_d_H_m_s));
            }
            attendUserCheckVoList.add(attendUserCheckVo);
            attendCheckVo.setAttendUserCheckVoList(attendUserCheckVoList);
            map.put(DateUtil.dateConvertString(sysAttendRecord.getWorkDate(), DateUtil.y_M_d), attendCheckVo);
        }
        List<AttendCheckVo> attendCheckVoList = map.values().stream().collect(Collectors.toList());


        attendCheckVoList.sort(Comparator.comparing(AttendCheckVo::getDate));

        return attendCheckVoList;
    }

    private void detailData(List<AttendUserVo> attendUserVoList, String startDate, String endDate) {
        List<String> userIdList = attendUserVoList.stream().map(AttendUserVo::getDingUserId).collect(Collectors.toList());
        DingTalkAttendUserCheckRequest request = new DingTalkAttendUserCheckRequest();
        request.setUserIdList(userIdList);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        List<DingTalkAttendUserCheckRes> dingTalkAttendUserCheckResList = remoteDingTalkAttendanceService.syncgAttendUserCheck(request, SecurityConstants.INNER).getData();

        for (DingTalkAttendUserCheckRes dingTalkAttendUserCheckRes : dingTalkAttendUserCheckResList) {
            SysAttendRecord sysAttendRecord = new SysAttendRecord();
            BeanUtil.copyProperties(dingTalkAttendUserCheckRes, sysAttendRecord);
            baseMapper.insert(sysAttendRecord);
        }
    }
}
