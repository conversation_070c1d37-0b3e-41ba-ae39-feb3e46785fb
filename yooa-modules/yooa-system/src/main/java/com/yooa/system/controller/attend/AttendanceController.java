package com.yooa.system.controller.attend;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.system.domain.dto.UserAttendCheckQuery;
import com.yooa.system.domain.vo.AttendCheckVo;
import com.yooa.system.service.SyncAttendUserCheckService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 参数配置 - 考勤
 */
@AllArgsConstructor
@RestController
@RequestMapping("/attendance")
public class AttendanceController extends BaseController {



    @Autowired
    private SyncAttendUserCheckService syncAttendUserCheckService;



    @GetMapping("/queryAttendList")
    public R<List<AttendCheckVo>> queryAttendList(UserAttendCheckQuery query) {
        return R.ok(syncAttendUserCheckService.queryAttendList(query));
    }
}
