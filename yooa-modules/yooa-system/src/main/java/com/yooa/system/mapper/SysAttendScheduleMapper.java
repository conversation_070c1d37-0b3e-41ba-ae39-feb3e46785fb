package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.system.api.domain.SysAttendSchedule;
import com.yooa.system.domain.vo.AttendScheduleVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 考勤组
 */
public interface SysAttendScheduleMapper extends BaseMapper<SysAttendSchedule> {

    public void deleteByTime(@Param("startDate")Date startTime, @Param("endDate")Date endTime);


    public List<AttendScheduleVo> queryScheduleByUserAndDate(@Param("startDate")Date startTime, @Param("endDate")Date endTime, @Param("userId")Long userId);
}




