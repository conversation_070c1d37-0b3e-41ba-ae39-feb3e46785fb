package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysRecruit;
import com.yooa.system.api.domain.SysRole;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.SysUserRole;
import com.yooa.system.api.domain.dto.RoleEditDto;
import com.yooa.system.api.domain.dto.RoleSaveDto;
import com.yooa.system.api.domain.query.RoleQuery;
import com.yooa.system.api.domain.vo.SysRecruitVo;
import com.yooa.system.api.domain.vo.SysRoleVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * 招聘计划业务层
 *
 * <AUTHOR>
 */
public interface RecruitService extends IService<SysRecruit> {
    /**
     * 分页查询
     * @return 招聘计划集合信息
     */
    public List<SysRecruitVo> selectRecruitList(Page<SysRecruitVo> page,@Param("query") SysRecruitVo vo);

    /**
     * @return 查询招聘计划 属于同一个流程的一起返回
     */
    public List<HashMap<String, Object>> getAllRecruitList(@Param("query")SysRecruitVo vo);

    /**
     * 导入招聘计划数据
     */
    public String importRecruit(MultipartFile file,String month) throws Exception;

    /**
     * 批量修改招聘计划
     */
    public String updateRecruit(SysRecruitVo vo) throws Exception;

    /**
     * 修改单个招聘计划
     */
    public String updateRecruitOne(SysRecruitVo vo) throws Exception;

    /**
     * 批量取消计划
     */
    public String editBatch(SysRecruitVo vo) throws Exception;

    /**
     * 删除/退回
     * @param sysRecruit
     * @return
     */
    public int deleteRecruitByIds(SysRecruitVo sysRecruit);


    /**
     * 追回
     * @param sysRecruit
     * @return
     */
    public int recoverRecruitByIds(SysRecruitVo sysRecruit);

    /**
     * 通过
     * @param sysRecruit
     * @return
     */
    public int passRecruit(SysRecruitVo sysRecruit);

    /**
     * 统计各个部门需求岗位
     * @return
     */
    public List<SysRecruitVo> getRecruitDeptCount(Page<SysRecruitVo> page,@Param("query") SysRecruitVo vo);

    /**
     * 根据月份统计我的招聘计划
     * @return
     */
    public SysRecruitVo getRecruitByMouthCount(SysRecruitVo vo);

}
