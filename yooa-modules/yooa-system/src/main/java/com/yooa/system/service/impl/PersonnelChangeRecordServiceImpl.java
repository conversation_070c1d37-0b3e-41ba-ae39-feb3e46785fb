package com.yooa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.system.api.domain.SysPersonnelChangeRecord;
import com.yooa.system.api.domain.query.PersonnelChangeRecordQuery;
import com.yooa.system.api.domain.vo.PersonnelChangeRecordVo;
import com.yooa.system.mapper.SysPersonnelChangeRecordMapper;
import com.yooa.system.service.PersonnelChangeRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 人员变动记录 - 业务实现层
 */
@Service
public class PersonnelChangeRecordServiceImpl extends ServiceImpl<SysPersonnelChangeRecordMapper, SysPersonnelChangeRecord>
        implements PersonnelChangeRecordService {

    @Override
    public List<PersonnelChangeRecordVo> getPersonnelChangeRecordList(PersonnelChangeRecordQuery query) {
        return baseMapper.selectPersonnelChangeRecordList(query);
    }
}




