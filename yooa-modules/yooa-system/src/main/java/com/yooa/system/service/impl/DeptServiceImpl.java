package com.yooa.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.UserConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.text.Convert;
import com.yooa.common.core.utils.SpringUtils;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.SysRole;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.vo.SysUserViewVo;
import com.yooa.system.domain.vo.DeptAndUserVo;
import com.yooa.system.domain.vo.TreeSelect;
import com.yooa.system.mapper.SysDeptMapper;
import com.yooa.system.mapper.SysRoleMapper;
import com.yooa.system.mapper.SysUserMapper;
import com.yooa.system.service.DeptService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门管理 - 服务实现层
 */
@AllArgsConstructor
@Service
public class DeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements DeptService {
    private final SysRoleMapper roleMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(DeptQuery query) {
        return baseMapper.selectDeptList(query);
    }

    @Override
    public SysDept getDeptById(Long deptId) {
        return baseMapper.selectDeptById(deptId);
    }

    @Override
    public List<TreeSelect> selectAuthDeptTreeList(DeptQuery query) {
        List<SysDept> deptList = SpringUtils.getAopProxy(this).selectDeptList(query);
        return buildDeptTreeSelect(deptList);
    }

    @Override
    public List<TreeSelect> selectDeptTreeList(DeptQuery query) {
        List<SysDept> deptList = baseMapper.selectDeptList(query);
        return buildDeptTreeSelect(deptList);
    }

    @Override
    public List<TreeSelect> selectDeptTreeChild(DeptQuery query) {
        //获取当前部门
        DeptQuery queryParent=new DeptQuery();
        queryParent.setDeptIds(query.getDeptIds());
        List<SysDept> deptList = SpringUtils.getAopProxy(this).selectDeptList(queryParent);
        List<SysDept> deptTrees = buildDeptTree(deptList);
        List<TreeSelect> list= deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
        for (TreeSelect treeSelect : list) {
            //获取当前部门的子部门
            DeptQuery queryChild=new DeptQuery();
            queryChild.setParentId(treeSelect.getId());
            List<SysDept> deptListChild = SpringUtils.getAopProxy(this).selectDeptList(queryChild);
            List<SysDept> deptTreesChild = buildDeptTree(deptListChild);
            List<TreeSelect> childList= deptTreesChild.stream().map(TreeSelect::new).collect(Collectors.toList());
            treeSelect.setChildren(childList);
        }
        return list;
    }


    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).toList();
        for (SysDept dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override
    public List<Long> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectRoleById(roleId);
        return baseMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据角色ID查询父部门为世纪征途的部门
     */
    @Override
    public List<Long> selectSJListByRoleId(Long roleId) {
        return baseMapper.selectSJListByRoleId(roleId);
    }

    @Override
    public int selectNormalChildrenDeptById(Long deptId) {
        return baseMapper.selectNormalChildrenDeptById(deptId);
    }

    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = baseMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    @Override
    public boolean checkDeptExistUser(Long deptId) {
        int result = baseMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    @Override
    public boolean checkDeptNameUnique(SysDept dept) {
        Long deptId = ObjUtil.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = baseMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (ObjUtil.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public void checkDeptDataScope(Long deptId) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            DeptQuery dept = new DeptQuery();
            dept.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (CollUtil.isEmpty(depts)) {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    @Override
    public int insertDept(SysDept dept) {
        SysDept info = baseMapper.selectById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!DictConstants.SYS_DISABLE_NO.equals(info.getStatus())) {
            throw new ServiceException("部门停用，不允许新增");
        }
        String ancestorsNames = info.getParentId() != 0 ? info.getAncestorsNames() + "-" + info.getDeptName() : info.getDeptName();
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        dept.setAncestorsNames(ancestorsNames);
        dept.setHierarchy(info.getHierarchy() + 1);
        dept.setDeptType(info.getDeptType());
        return baseMapper.insert(dept);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateDept(SysDept dept) {
        // 父部门
        SysDept parentDept = baseMapper.selectById(dept.getParentId());
        // 旧部门
        SysDept oldDept = baseMapper.selectById(dept.getDeptId());

        // 只有当部门名称或者部门结构变动的时候才遍历修改子部门
        if (!oldDept.getDeptName().equals(dept.getDeptName()) || !oldDept.getParentId().equals(dept.getParentId())) {
            if (ObjUtil.isNotNull(parentDept)) {
                String ancestorsNames = parentDept.getParentId() != 0 ? parentDept.getAncestorsNames() + "-" + parentDept.getDeptName() : parentDept.getDeptName();
                dept.setAncestors(parentDept.getAncestors() + "," + parentDept.getDeptId());
                dept.setAncestorsNames(ancestorsNames);
                dept.setHierarchy(parentDept.getHierarchy() + 1);
            }

            // 获取所有下级部门集合
            List<SysDept> sysDeptList = baseMapper.selectAllChildrenDeptById(dept.getDeptId());
            if (CollUtil.isNotEmpty(sysDeptList)) {
                // 处理数据
                List<SysDept> updateDeptList = updateDeptChildren(dept, buildDeptTree(sysDeptList), new ArrayList<>());
                // 批量修改
                baseMapper.updateDeptChildren(updateDeptList);
            }
        }

        if (DictConstants.SYS_DISABLE_NO.equals(dept.getStatus()) && StrUtil.isNotEmpty(dept.getAncestors())
                && !StrUtil.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return baseMapper.updateById(dept);
    }


    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        baseMapper.updateDeptStatusNormal(deptIds);
    }

    public List<SysDept> updateDeptChildren(SysDept parentDept, List<SysDept> children, List<SysDept> updateList) {
        String ancestorsNames = parentDept.getParentId() != 0 ? parentDept.getAncestorsNames() + "-" + parentDept.getDeptName() : parentDept.getDeptName();
        String ancestors = parentDept.getAncestors() + "," + parentDept.getDeptId();
        int hierarchy = parentDept.getHierarchy() + 1;

        for (SysDept child : children) {
            child.setAncestorsNames(ancestorsNames);
            child.setAncestors(ancestors);
            child.setHierarchy(hierarchy);
            child.setLowermost(DictConstants.SYS_DEFAULT_YES);

            if (CollUtil.isNotEmpty(child.getChildren())) {
                child.setLowermost(DictConstants.SYS_DEFAULT_NO);
                updateDeptChildren(child, child.getChildren(), updateList);
            }
            updateList.add(child);
        }
        return updateList;
    }


    private void recursionFn(List<SysDept> list, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext()) {
            SysDept n = (SysDept) it.next();
            if (ObjUtil.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    private boolean hasChild(List<SysDept> list, SysDept t) {
        return !getChildList(list, t).isEmpty();
    }

    @Override
    public R<List<SysDept>> getDeptList(DeptQuery query) {
        return R.ok(baseMapper.selectDeptList(query));
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> getDeptList(Page<SysDept> page, DeptQuery query) {
        return baseMapper.selectDeptList(page, query);
    }

    @Override
    public DeptAndUserVo getDeptTreeAndUserVo() {
        // 查询部门树
        List<SysDept> deptList = baseMapper.selectDeptList(new DeptQuery());
        List<TreeSelect> treeSelectList =  buildDeptTreeSelect(deptList);
        // 查询在职生效人员
        List<SysUserViewVo> sysUserList = sysUserMapper.selectAllUser();
        DeptAndUserVo deptAndUserVo = new DeptAndUserVo();
        deptAndUserVo.setTreeSelectList(treeSelectList);
        deptAndUserVo.setSysUserList(sysUserList);
        return deptAndUserVo;
    }
}
