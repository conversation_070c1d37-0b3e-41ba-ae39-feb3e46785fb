package com.yooa.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.extend.api.RemoteGoalExtendService;
import com.yooa.extend.api.domain.vo.PlanCountVo;
import com.yooa.system.api.RemoteApprovalService;
import com.yooa.system.api.domain.*;
import com.yooa.system.api.domain.query.RosterQuery;
import com.yooa.system.api.domain.vo.*;
import com.yooa.system.mapper.*;
import com.yooa.system.service.*;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
public class PocessBacklogServiceImpl extends ServiceImpl<SysProcessBacklogMapper, SysProcessBacklog> implements ProcessBacklogService {

    private final RemoteApprovalService remoteApprovalService;
    @Autowired
    private SysUserMapper sysUserMapper;
    private final SysRecruitUserMapper sysRecruitUserMapper;
    private final SysRecruitCountMapper sysRecruitCountMapper;
    private final SysRecruitMapper sysRecruitMapper;
    private final SysRecruitUserSubmitMapper sysRecruitUserSubmitMapper;
    private final DeptService deptService;
    private final SysRecruitOperateMapper sysRecruitOperateMapper;
    private final ReservationService reservationService;
    private final SysInterviewMapper sysInterviewMapper;
    private final SysRosterMapper sysRosterMapper;
    private final RemoteGoalExtendService remoteGoalExtendService;
    private final SysRosterUserMapper sysRosterUserMapper;
    private final SysDeptMapper sysDeptMapper;
    private final PostService postService;
    private final UserService userService;
    /**
     * 查询我的代办发起(业务流程和自定义流程代办一起返回)
     * @return
     */
    @Override
    public SysProcessBacklogVo selectProcessBacklogList(SysProcessBacklogVo vo) {
        SysProcessBacklogVo sysProcessBacklogVo=new SysProcessBacklogVo();
        List<SysProcessBacklogVo> processBacklogList=new ArrayList<>();
        List<TaskMgmtDaVO> TaskList=new ArrayList<>();
        //默认查所有
        if (vo.getTypeNameBig()==null || vo.getTypeNameBig().isEmpty()) {
            vo.setTypeNameBig("全部");
        }
        //代办,查询审批和草稿状态
        if (vo.getGenre()==1){
            //查询别的小类型时过滤掉自定义流程
            if (vo.getTypeNameBig().equals("全部")){
                TaskList=remoteApprovalService.getAllPcToDoList(vo,SecurityConstants.INNER).getData();
            }
            //第一次不传状态查所有审批中,审批通过和草稿状态的流程
            if (vo.getState()==null || vo.getState().isEmpty()) {
                vo.setState("1");
            }
            vo.setApproverId(SecurityUtils.getUserId());
            processBacklogList=this.baseMapper.selectProcessBacklogList(vo);
        }
        //发起
        if (vo.getGenre()==2){
            //查询别的小类型时过滤掉自定义流程
            if (vo.getTypeNameBig().equals("全部")){
                TaskList=remoteApprovalService.getTaskMgmtList(vo,SecurityConstants.INNER).getData();
            }

            vo.setApplicantId(SecurityUtils.getUserId());
            processBacklogList=this.baseMapper.selectProcessBacklogList(vo);
        }
        sysProcessBacklogVo.setPlanCount(processBacklogList.size());
        if (TaskList!=null){
            for (TaskMgmtDaVO taskMgmtVO : TaskList) {
                SysProcessBacklogVo backlog = new SysProcessBacklogVo();
                //把流程所需字段移过来
                backlog.setFormCode(taskMgmtVO.getProcessKey());
                backlog.setIsLowCodeFlow(taskMgmtVO.getIsLowCodeFlow());
                backlog.setIsOutSideAccessProc(taskMgmtVO.getIsOutSideProcess());
                backlog.setTaskId(taskMgmtVO.getTaskId());
                backlog.setProcessNumber(taskMgmtVO.getProcessNumber());
                // 排序字段 使用系统默认时区
                LocalDateTime localDateTime = LocalDateTime.ofInstant(taskMgmtVO.getCreateTime().toInstant(), ZoneId.systemDefault());
                backlog.setCreateTime(localDateTime);
                //标题
                String title = taskMgmtVO.getDescription();
                backlog.setTitle(title);
                //申请人 申请人ID
                backlog.setApplicantId(Long.valueOf(taskMgmtVO.getUserId()));
                SysUser sysUser = sysUserMapper.selectById(taskMgmtVO.getUserId());
                backlog.setApplicant(sysUser.getNickName());
                //审批人 审批人ID
                if (taskMgmtVO.getApprover()!=0){
                    backlog.setApproverId(Long.valueOf(taskMgmtVO.getApprover()));
                    SysUser sysUser1 = sysUserMapper.selectById(taskMgmtVO.getApprover());
                    backlog.setApprover(sysUser1.getNickName());
                }
                //类别（流程大类名称）
                String type = taskMgmtVO.getDescription().substring(taskMgmtVO.getDescription().indexOf('-') + 1);
                backlog.setTypeNameBig(type);

                backlog.setState(String.valueOf(taskMgmtVO.getProcessState()));
                //类别
                backlog.setType(2);
                processBacklogList.add(backlog);
            }
        }
        // 使用 Stream.sorted() 进行排序
        List<SysProcessBacklogVo> sortedprocessBacklogList = processBacklogList.stream()
                .sorted((e1, e2) -> e2.getCreateTime().compareTo(e1.getCreateTime())) // 降序排序
                .toList();
        sysProcessBacklogVo.setProcessBacklogListPage(sortedprocessBacklogList);
        sysProcessBacklogVo.setTotalCount(processBacklogList.size());
        return sysProcessBacklogVo;
    }

    /**
     * 手动分页列表
     * @param sourceList
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public List<SysProcessBacklogVo> getPageProcessBack(List<SysProcessBacklogVo> sourceList, int page, int pageSize) {
        if (pageSize <= 0 || page <= 0) {
            throw new IllegalArgumentException("页码和每页大小必须大于0");
        }
        int fromIndex = (page - 1) * pageSize;
        if (sourceList.size() <= fromIndex) {
            return new ArrayList<>(); // 返回空列表
        }
        int toIndex = Math.min(fromIndex + pageSize, sourceList.size());
        return sourceList.subList(fromIndex, toIndex);
    }

    /**
     * 根据流程ID查询详情（人员信息和统计信息）
     * @param vo
     * @return
     */
    @Override
    public SysProcessBacklogVo getDetails(SysProcessBacklogVo vo) {
        //根据流程ID查询人员信息和统计信息
        //已提交显示所有，未提交显示没有本月没有提交招聘任务的专员
        SysRecruitUserVo userVo=new SysRecruitUserVo();
        userVo.setProcessId(vo.getId());
        //获取发起人部门
        SysProcessBacklog backlog=this.baseMapper.selectById(vo.getId());
        LambdaQueryWrapper<SysRosterUser> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(SysRosterUser::getUserId, backlog.getApplicantId());
        SysRosterUser rosterUser = sysRosterUserMapper.selectOne(wrapper1);
        SysRoster roster = sysRosterMapper.selectById(rosterUser.getRosterId());
        SysDept dept=sysDeptMapper.selectById(roster.getDeptId());
        vo.setOperateDeptName(dept.getDeptName());
        vo.setCreateDate(backlog.getCreateTime());
        userVo.setProcessId(vo.getId());
        List<SysRecruitUserVo> sysRecruitUserList = sysRecruitUserMapper.getNotPagetUserList(userVo);
        grantRecruitUser(sysRecruitUserList,vo);
        if (vo.getIsSubmit().equals("1")){
            //如果已经生成了就使用生成数据，未生成使用初始数据
            //根据流程ID查询人员信息
            userVo.setProcessId(vo.getId());
            List<SysRecruitUserVo> sysRecruitUserListLast = sysRecruitUserMapper.getNotPagetUserList(userVo);
            if (!sysRecruitUserListLast.isEmpty()) {
                grantRecruitUser(sysRecruitUserListLast,vo);
                vo.setRecruitUserVoList(sysRecruitUserListLast);
            }else {
                vo.setRecruitUserVoList(sysRecruitUserList);
            }
        }
        if (vo.getIsSubmit().equals("2")){
            List<SysRecruitUserVo> sysRecruitUser2=new ArrayList<>();
            //查询当月流程招聘提交数据人员，看哪些人员不在这个里面
            YearMonth currentYearMonth = YearMonth.now();
            for (SysRecruitUserVo sysRecruitUser1 : sysRecruitUserList ) {
                LambdaQueryWrapper<SysProcessBacklog> wrapperBacklog = new LambdaQueryWrapper<>();
                wrapperBacklog.eq(SysProcessBacklog::getTypeName, "招聘");
                wrapperBacklog.like(SysProcessBacklog::getCreateTime, currentYearMonth);
                wrapperBacklog.eq(SysProcessBacklog::getCreateBy,sysRecruitUser1.getUserId());
                //判断这个用户这个月是否有招聘流程提交
                List<SysProcessBacklog> backlogList = this.baseMapper.selectList(wrapperBacklog);
                if (backlogList.isEmpty()){
                    sysRecruitUser2.add(sysRecruitUser1);
                }
            }
            vo.setRecruitUserVoList(sysRecruitUser2);
        }

        //查询统计信息
        LambdaQueryWrapper<SysRecruitCount> wrapperCount = new LambdaQueryWrapper<>();
        wrapperCount.eq(SysRecruitCount::getProcessId, vo.getId());
        SysRecruitCount sysRecruitCount=sysRecruitCountMapper.selectOne(wrapperCount);
        vo.setSysRecruitCount(sysRecruitCount);
        return vo;
    }

    /**
     * 根据流程ID查询招聘计划
     * @param id
     * @return
     */
    @Override
    public List<SysRecruit> getSysRecruitList(Long id) {
        LambdaQueryWrapper<SysRecruit> wrapperSysRecruit = new LambdaQueryWrapper<>();
        wrapperSysRecruit.eq(SysRecruit::getProcessId, id);
        return sysRecruitMapper.selectList(wrapperSysRecruit);
    }

    @Override
    public int deleteBacklog(Long id) {
        SysProcessBacklog sysProcessBacklog=this.baseMapper.selectById(id);
        //删除流程，删除流程统计表，删除计划，删除人员，删除发布，新增操作记录
        if (sysProcessBacklog.getState().equals("3") ||sysProcessBacklog.getState().equals("4")) {
            //1流程删除
            this.baseMapper.deleteById(id);
            //2.删除流程统计表
            LambdaQueryWrapper<SysRecruitCount> wrapperCount = new LambdaQueryWrapper<>();
            wrapperCount.eq(SysRecruitCount::getProcessId, id);
            sysRecruitCountMapper.delete(wrapperCount);
            //3.删除计划,6.新增操作记录
            LambdaQueryWrapper<SysRecruit> wrapperSysRecruit = new LambdaQueryWrapper<>();
            wrapperSysRecruit.eq(SysRecruit::getProcessId, id);
            List<SysRecruit> sysRecruitsList=sysRecruitMapper.selectList(wrapperSysRecruit);
            for (SysRecruit sysRecruit:sysRecruitsList) {
                SysRecruitVo recruitVo=new SysRecruitVo();
                BeanUtils.copyProperties(sysRecruit, recruitVo);
                recruitVo.setOperationType("删除");
                SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
                sysRecruitOperateMapper.insert(sysRecruitOperate);
                sysRecruitMapper.deleteById(sysRecruit.getId());
            }
            //4.删除人员
            LambdaQueryWrapper<SysRecruitUser> wrapperUser = new LambdaQueryWrapper<>();
            wrapperUser.eq(SysRecruitUser::getProcessId, id);
            sysRecruitUserMapper.delete(wrapperUser);

            //5.删除发布
            LambdaQueryWrapper<SysRecruitUserSubmit> wrapperUserSubmit = new LambdaQueryWrapper<>();
            wrapperUserSubmit.eq(SysRecruitUserSubmit::getProcessId, id);
            sysRecruitUserSubmitMapper.delete(wrapperUserSubmit);
        }else{
            return 2;
        }
        return 1;
    }

    /**
     * 发布招聘计划
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int releasePlan(SysProcessBacklogVo vo){
        //操作类型
        String operateType="";
        //本次分配数量
        Long commissionerTotal=0L;
        Long manageTotal=0L;
        List<SysRecruitUser> listUser = vo.getRecruitUserList();
        for (SysRecruitUser sysRecruitUser : listUser) {
            Long commissioner = sysRecruitUser.getCommissioner();
            Long manage= sysRecruitUser.getManage();
            if (commissioner!=null){
                commissionerTotal=commissionerTotal+commissioner;
            }
            if (manage!=null){
                manageTotal=manageTotal+manage;
            }
        }
        if (commissionerTotal==0L && manageTotal==0L){
            return 0;
        }
        try{
            //发布
            if (vo.getOperationType()==1){
                //判断分配数量
                int state=getRecruitCount(commissionerTotal,manageTotal,vo.getId());
                if (state==1 || state==2){
                    return state;
                }
                SysProcessBacklog backlog=this.baseMapper.selectById(vo.getId());
//                if (!backlog.getState().equals("2")) {
//                    throw new ServiceException("发布失败，请通过流程之后按计划分配再发布！");
//
//                }
                operateType="发布";

                LambdaQueryWrapper<SysRecruit> wrapperSysRecruit = new LambdaQueryWrapper<>();
                wrapperSysRecruit.eq(SysRecruit::getProcessId, vo.getId());
                List<SysRecruit> recruitList=sysRecruitMapper.selectList(wrapperSysRecruit);
                String demandDepartment="";
                for (SysRecruit sysRecruit:recruitList){
                    if (demandDepartment.isEmpty()){
                        demandDepartment=sysRecruit.getDemandDepartment();
                    }
                    if(!demandDepartment.contains(sysRecruit.getDemandDepartment())){
                        demandDepartment=demandDepartment+","+sysRecruit.getDemandDepartment();
                    }
                }

                //新增发布列表数据
                List<SysRecruitUser> list = vo.getRecruitUserList();
                for (SysRecruitUser sysRecruitUser : list) {
                    Long commissioner = sysRecruitUser.getCommissioner();
                    Long manage= sysRecruitUser.getManage();
                    if (commissioner!=null || manage!=null){
                        SysRecruitUserSubmit userSubmit = new SysRecruitUserSubmit();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm");
                        userSubmit.setUploadTime(formatter.format(vo.getCreateDate()));
                        userSubmit.setDemandDepartment(demandDepartment);
                        userSubmit.setName(sysRecruitUser.getName());
                        userSubmit.setHireDate(sysRecruitUser.getHireData());
                        userSubmit.setUserId(sysRecruitUser.getUserId());
                        userSubmit.setCommissioner(sysRecruitUser.getCommissioner());
                        userSubmit.setManage(sysRecruitUser.getManage());
                        userSubmit.setProcessId(vo.getId());
                        //新增操作计划归属类型字段
                        int recruitType=0;

//                        //查询流程发起人 人员角色
//                        SysUserVo sysUser = userService.selectUserById(backlog.getApplicantId());
//                        List<Long> roleIdList= sysUser.getRoles().stream().map(SysRole::getRoleId).toList();
//                        //根据角色查负责部门
//
//                        List<Long> deptIdList=new ArrayList<>();
//                        for (Long roleId : roleIdList) {
//                            List<Long> listByRoleId=deptService.selectSJListByRoleId(roleId);
//                            deptIdList.addAll(listByRoleId);
//                        }
//                        LambdaQueryWrapper<SysDept> wrapperDept = new LambdaQueryWrapper<>();
//                        if (!deptIdList.isEmpty()){
//                            wrapperDept.in(SysDept::getDeptId, deptIdList);
//                        }
//                        List<SysDept> sysDeptList = sysDeptMapper.selectList(wrapperDept);
//                        for (SysDept sysDept : sysDeptList) {
//                            if (recruitType.isEmpty()){
//                                recruitType=sysDept.getDeptName();
//                            }
//                            if(!recruitType.contains(sysDept.getDeptName())){
//                                recruitType=recruitType+","+sysDept.getDeptName();
//                            }
//                        }
                        if (demandDepartment.contains("推广") || demandDepartment.equals("市场")){
                            recruitType=1;
                        }
                        if (demandDepartment.contains("运营")){
                            recruitType=2;
                        }
                        if (demandDepartment.contains("人事")){
                            recruitType=3;
                        }
                        if (demandDepartment.contains("平台")){
                            recruitType=4;
                        }
                        userSubmit.setRecruitType(recruitType);
                        sysRecruitUserSubmitMapper.insert(userSubmit);
                    }
                }

                //保存发布数据
                List<SysRecruitUser> recruitUserList=vo.getRecruitUserList();
                for (SysRecruitUser sysRecruitUser:recruitUserList){
                    sysRecruitUserMapper.updateById(sysRecruitUser);
                }

            }
            //保存，修改流程状态为草稿保存状态，流程人员分配数量保存
            if (vo.getOperationType()==2){
                //判断分配数量
                getRecruitCount(commissionerTotal,manageTotal,vo.getId());
                operateType="保存";
                SysProcessBacklog sysProcessBacklog=this.baseMapper.selectById(vo.getId());
                sysProcessBacklog.setState("5");
                baseMapper.updateById(sysProcessBacklog);
                List<SysRecruitUser> recruitUserList=vo.getRecruitUserList();
                for (SysRecruitUser sysRecruitUser:recruitUserList){
                    sysRecruitUserMapper.updateById(sysRecruitUser);
                }
            }
            //追回
            if (vo.getOperationType()==3){
                operateType="追回";
                SysProcessBacklog sysProcessBacklog=this.baseMapper.selectById(vo.getId());
                //修改审批人，流程状态改为追回
                sysProcessBacklog.setApprover("");
                sysProcessBacklog.setApproverId(null);
                sysProcessBacklog.setState("4");
                this.baseMapper.updateById(sysProcessBacklog);
            }
            //根据流程ID获取下面所有的计划添加操作记录
            LambdaQueryWrapper<SysRecruit> wrapperSysRecruit = new LambdaQueryWrapper<>();
            wrapperSysRecruit.eq(SysRecruit::getProcessId, vo.getId());
            List<SysRecruit> recruitList=sysRecruitMapper.selectList(wrapperSysRecruit);
            for (SysRecruit sysRecruit:recruitList){
                SysRecruitVo recruitVo=new SysRecruitVo();
                BeanUtils.copyProperties(sysRecruit, recruitVo);
                recruitVo.setOperationType(operateType);
                SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
                sysRecruitOperateMapper.insert(sysRecruitOperate);
            }
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }
        return 3;
    }

    /**
     * 重新发起审批
     * @param vo
     * @return
     */
    @Override
    public int initiate(SysProcessBacklogVo vo) {
        //修改流程状态，修改审批人为行政部负责人
        SysProcessBacklog sysProcessBacklog=this.baseMapper.selectById(vo.getId());
        SysDept sysDept=deptService.getDeptById(90L);
        sysProcessBacklog.setApprover(sysDept.getLeaderName());
        sysProcessBacklog.setApproverId(sysDept.getLeaderId());
        sysProcessBacklog.setState("1");
        this.baseMapper.updateById(sysProcessBacklog);
        //计划修改为重新上传的，统计数量修改
        List<SysRecruit> listRecruit = vo.getRecruitList();
        Long count=0L;
        Long commissionerTotal=0L;
        Long manageTotal=0L;
        for (SysRecruit recruit:listRecruit){
            //计划状态修改为未确定
            recruit.setState("0");
            sysRecruitMapper.updateById(recruit);
            commissionerTotal=commissionerTotal+recruit.getCommissioner();
            manageTotal=manageTotal+recruit.getManage();

            //操作记录表新增记录
            SysRecruitVo recruitVo=new SysRecruitVo();
            BeanUtils.copyProperties(recruit, recruitVo);
            recruitVo.setOperationType("重新上传");
            SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
            sysRecruitOperateMapper.insert(sysRecruitOperate);
        }
        //重新计算统计表
        count=commissionerTotal+manageTotal;
        LambdaQueryWrapper<SysRecruitCount> wrapperCount = new LambdaQueryWrapper<>();
        wrapperCount.eq(SysRecruitCount::getProcessId, vo.getId());
        SysRecruitCount sysRecruitCount=sysRecruitCountMapper.selectOne(wrapperCount);
        sysRecruitCount.setTotal(count);
        sysRecruitCount.setCommissionerTotal(commissionerTotal);
        sysRecruitCount.setManageTotal(manageTotal);
        sysRecruitCount.setCommissionerRemaining(commissionerTotal);
        sysRecruitCount.setManageRemaining(manageTotal);
        sysRecruitCountMapper.updateById(sysRecruitCount);
        return 1;
    }

    @Override
    public TodayCountVo getTodayCountVo() {
        TodayCountVo todayCountVo=new TodayCountVo();
        //我的代办
        SysProcessBacklogVo vo=new SysProcessBacklogVo();
        vo.setGenre(1);
        //自定义流程
        List<TaskMgmtDaVO> TaskList=remoteApprovalService.getAllPcToDoList(vo,SecurityConstants.INNER).getData();
        SysProcessBacklogVo vo1=new SysProcessBacklogVo();
        vo1.setGenre(vo.getGenre());
        vo1.setApproverId(SecurityUtils.getUserId());
        //业务
        List<SysProcessBacklogVo> processBacklogList=this.baseMapper.selectProcessBacklogList(vo1);
        if (TaskList!=null){
            todayCountVo.setRepresentativeNumber(processBacklogList.size()+TaskList.size());
        }else{
            todayCountVo.setRepresentativeNumber(processBacklogList.size());
        }
        //我的今日面试人数
        List<SysReservation> reservationList=new ArrayList<>();
        reservationList=reservationService.getToDayReservationList();
        todayCountVo.setToDayInterview(reservationList.size());

        //我邀约的今日入职人数
        InterviewListVo query=new InterviewListVo();
        LocalDate date=LocalDate.now();
        query.setRecruitUserId(SecurityUtils.getUserId());
        query.setEstimateEmploymentDate(date);
        List<InterviewListVo> listVoList=sysInterviewMapper.selectInterview(query);
        todayCountVo.setToDayEntry(listVoList.size());


        //我邀约的今日转正人数
        Date now = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time=simpleDateFormat.format(now);
        RosterQuery q1=new RosterQuery();
        q1.setPlanFormalDate(time);
        q1.setInviteUerId(SecurityUtils.getUserId());
        List<SysRosterVo> planFormalList=sysRosterMapper.selectRoster(q1);
        todayCountVo.setToDayBecome(planFormalList.size());

        //我邀约的今日离职人数
        RosterQuery q2=new RosterQuery();
        q2.setResignationDate(time);
        q2.setInviteUerId(SecurityUtils.getUserId());
        List<SysRosterVo> planFormalList1=sysRosterMapper.selectRoster(q2);
        todayCountVo.setToDayResign(planFormalList1.size());

        return todayCountVo;
    }

    @Override
    public PlanCountVo getPlanCount(SysProcessBacklogVo vo) {
        PlanCountVo planCountVo=remoteGoalExtendService.getPlanCount(SecurityConstants.INNER).getData();

        //代办-招聘数量
        //我的代办流程关联的计划数量
        List<SysProcessBacklog> BacklogList=new ArrayList<>();
        LambdaQueryWrapper<SysProcessBacklog> wrapperLog = new LambdaQueryWrapper<>();
        if(vo.getGenre()==1){
            //代办查询审批中 审批通过 草稿状态的
            wrapperLog.eq(SysProcessBacklog::getApproverId, SecurityUtils.getUserId());
            List<String> stateList=new ArrayList<>();
            stateList.add("1");
            stateList.add("2");
            stateList.add("5");
            wrapperLog.in(SysProcessBacklog::getState, stateList);
            wrapperLog.eq(SysProcessBacklog::getTypeName, "招聘");
            BacklogList=this.baseMapper.selectList(wrapperLog);
        }
        if(vo.getGenre()==2){
            wrapperLog.eq(SysProcessBacklog::getApplicantId, SecurityUtils.getUserId());
            wrapperLog.eq(SysProcessBacklog::getTypeName, "招聘");
            BacklogList=this.baseMapper.selectList(wrapperLog);
        }
        //计划数量
        List<SysRecruit> recruitList=new ArrayList<>();
        for(SysProcessBacklog backlog:BacklogList){
            LambdaQueryWrapper<SysRecruit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysRecruit::getProcessId, backlog.getId());
            List<SysRecruit> recruitList1=sysRecruitMapper.selectList(wrapper);
            recruitList.addAll(recruitList1);
        }
        planCountVo.setPlanCount(recruitList.size());
        return planCountVo;
    }

    /**
     * 设置操作记录
     * @param recruit
     * @return
     */
    public SysRecruitOperate setProperty(SysRecruitVo recruit) {
        SysRecruitOperate sysRecruitOperate=new SysRecruitOperate();
        sysRecruitOperate.setRecruitId(recruit.getId());
        sysRecruitOperate.setUserId(SecurityUtils.getUserId());
        sysRecruitOperate.setName(SecurityUtils.getUsername());
        sysRecruitOperate.setOperationType(recruit.getOperationType());
        sysRecruitOperate.setDept(recruit.getDemandDepartment());
        sysRecruitOperate.setManage(recruit.getManage());
        sysRecruitOperate.setCommissioner(recruit.getCommissioner());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDateTime = now.format(formatter);
        sysRecruitOperate.setOperateTime(formattedDateTime);
        int hour = now.getHour(); // 获取当前小时
        int minute = now.getMinute();// 获取当前分钟
        String stringHour=String.valueOf(hour);
        String stringMinute=String.valueOf(minute);
        if (hour<10){
            stringHour="0"+stringHour;
        }
        if (minute<10){
            stringMinute="0"+stringMinute;
        }
        sysRecruitOperate.setOperateDate(stringHour + ":" + stringMinute);
        return sysRecruitOperate;
    }

    /**
     * 给人员赋值岗位，角色，所在团队等信息
     *
     * @param sysRecruitUserList
     */
    public void grantRecruitUser(List<SysRecruitUserVo> sysRecruitUserList,SysProcessBacklogVo vo) {
        for (SysRecruitUserVo sysRecruitUserVo : sysRecruitUserList) {
            Long userId = sysRecruitUserVo.getUserId();
            LambdaQueryWrapper<SysRosterUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysRosterUser::getUserId, userId);
            SysRosterUser sysRosterUser = sysRosterUserMapper.selectOne(wrapper);
            if (sysRosterUser != null) {
                SysRoster SysRoster = sysRosterMapper.selectById(sysRosterUser.getRosterId());
                SysDept sysDept=sysDeptMapper.selectById(SysRoster.getDeptId());
                vo.setDeptName(vo.getDeptName());
                sysRecruitUserVo.setPost(SysRoster.getPostName());
                sysRecruitUserVo.setRole(SysRoster.getEmployeeRole());
                sysRecruitUserVo.setBelongingTeam(sysDept.getDeptName());
                //查询人员角色
                SysUserVo sysUser = userService.selectUserById(userId);
                List<Long> roleIdList= sysUser.getRoles().stream().map(SysRole::getRoleId).toList();
                //根据角色查负责部门
                List<Long> deptIdList=new ArrayList<>();
                for (Long roleId : roleIdList) {
                    List<Long> list=deptService.selectDeptListByRoleId(roleId);
                    deptIdList.addAll(list);
                }
                String responsibleTeam="";
                if (!deptIdList.isEmpty()) {
                    LambdaQueryWrapper<SysDept> wrapperDept = new LambdaQueryWrapper<>();
                    wrapperDept.in(SysDept::getDeptId, deptIdList);
                    List<SysDept> sysDeptList = sysDeptMapper.selectList(wrapperDept);

                    for (SysDept sysDept1:sysDeptList){
                        if (responsibleTeam.isEmpty()){
                            responsibleTeam=sysDept1.getDeptName();
                        }if (!responsibleTeam.contains(sysDept1.getDeptName())){
                            responsibleTeam=sysDept1.getDeptName()+","+responsibleTeam;
                        }
                    }
                }
                sysRecruitUserVo.setResponsibleTeam(responsibleTeam);
            }
        }
    }

    /**
     * 判断分配数量是否大于可用数量
     *
     * @param commissionerTotal
     * @param manageTotal
     * @param id
     */
    public int getRecruitCount(Long commissionerTotal, Long manageTotal, Long id) {

        //修改统计表中可用数量
        LambdaQueryWrapper<SysRecruitCount> wrapperCount = new LambdaQueryWrapper<>();
        wrapperCount.eq(SysRecruitCount::getProcessId, id);
        SysRecruitCount sysRecruitCount=sysRecruitCountMapper.selectOne(wrapperCount);
        if (commissionerTotal>sysRecruitCount.getCommissionerRemaining()){
            AjaxResult.error("专员分配数量不能大于可分配数！");
            return 1;
        }
        if (manageTotal>sysRecruitCount.getManageRemaining()){
            AjaxResult.error("管理分配数量不能大于可分配数！");
            return 2;
        }
        sysRecruitCount.setCommissionerRemaining(sysRecruitCount.getCommissionerRemaining()-commissionerTotal);
        sysRecruitCount.setManageRemaining(sysRecruitCount.getManageRemaining()-manageTotal);
        sysRecruitCountMapper.updateById(sysRecruitCount);
        return 3;
    }
}
