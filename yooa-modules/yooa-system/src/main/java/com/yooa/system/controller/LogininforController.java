package com.yooa.system.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.constant.CacheConstants;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.redis.service.RedisService;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.system.api.domain.SysLogininfor;
import com.yooa.system.api.domain.query.LogininforQuery;
import com.yooa.system.service.LogininforService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统访问记录
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@RestController
@RequestMapping("/logininfor")
public class LogininforController extends BaseController {
    private final LogininforService logininforService;
    private final RedisService redisService;

    @RequiresPermissions("system:logininfor:list")
    @GetMapping("/list")
    public AjaxResult list(Page<SysLogininfor> page, LogininforQuery query) {
        return success(page.setRecords(logininforService.selectLogininforList(page, query)));
    }

    @RequiresPermissions("system:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public AjaxResult remove(@PathVariable List<Long> infoIds) {
        return toAjax(logininforService.removeByIds(infoIds));
    }

    @RequiresPermissions("system:logininfor:remove")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean")
    public AjaxResult clean() {
        logininforService.cleanLogininfor();
        return success();
    }

    @RequiresPermissions("system:logininfor:unlock")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{userName}")
    public AjaxResult unlock(@PathVariable("userName") String userName) {
        redisService.deleteObject(CacheConstants.PWD_ERR_CNT_KEY + userName);
        return success();
    }

    @InnerAuth
    @PostMapping
    public AjaxResult add(@RequestBody SysLogininfor logininfor) {
        return toAjax(logininforService.save(logininfor));
    }
}
