package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.domain.SysYooaAttendGroup;
import com.yooa.system.domain.dto.AttendGroupRequest;
import com.yooa.system.domain.vo.AttendGroupRelationVo;
import com.yooa.system.domain.vo.AttendGroupViewVo;
import com.yooa.system.domain.vo.AttendGroupVo;

import java.util.List;

/**
 *考勤组 服务层
 *
 * <AUTHOR>
 */
public interface AttendGroupService extends IService<SysYooaAttendGroup> {

    /**
     *列表数据查询
     */
    List<AttendGroupVo> list(String groupName);

    /**
     * 删除考勤组
     */
    void delete(Long groupId);

    /**
     * 新增考勤组
     * @param request
     */
    void add(AttendGroupRequest request);

    /**
     * 查询考勤组详情
     */
    AttendGroupViewVo view(Long groupId);


    /**
     * 查询考勤组关联的排班以及关联员工考勤
     */
    AttendGroupRelationVo queryClassAndRelation(Long groupId);
    /**
     * 修改考勤组
     * @param request
     */
    void update(AttendGroupRequest request);

}
