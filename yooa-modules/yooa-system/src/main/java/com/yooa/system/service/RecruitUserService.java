package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysRecruitUser;
import com.yooa.system.api.domain.SysRecruitUserSubmit;
import com.yooa.system.api.domain.vo.SysProcessBacklogVo;
import com.yooa.system.api.domain.vo.SysRecruitUserSubmitVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface RecruitUserService extends IService<SysRecruitUser> {
    /**
     * @return 招聘计划人员新增
     */
    public int addRecruitUser(SysProcessBacklogVo vo);


    /**
     * @return 招聘计划人员删除
     */
    public int deleteRecruitUser(Long userId,Long processId);
}
