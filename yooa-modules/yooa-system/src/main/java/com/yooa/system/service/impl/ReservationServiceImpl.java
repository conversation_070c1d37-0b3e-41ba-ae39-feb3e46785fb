package com.yooa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.domain.*;
import com.yooa.system.api.domain.query.RosterQuery;
import com.yooa.system.api.domain.vo.SysReservationVo;
import com.yooa.system.api.domain.vo.SysRosterVo;
import com.yooa.system.mapper.*;
import com.yooa.system.service.ReservationService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 招聘计划业务层处理
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class ReservationServiceImpl extends ServiceImpl<SysReservationMapper, SysReservation> implements ReservationService {

    private final SysRecruitUserSubmitMapper sysRecruitUserSubmitMapper;
    private final SysRosterMapper sysRosterMapper;
    private final SysInterviewMapper sysInterviewMapper;

    /**
     * 分页查询预约面试列表
     *
     * @param page
     * @return
     */
    @Override
    public List<SysReservationVo> getSysReservationList(Page<SysReservationVo> page) {
        return  baseMapper.getReservationList(page);
    }

    /**
     * 不分页查询我的今日预约列表，和统计信息
     * @return
     */
    @Override
    public List<SysReservation> getToDayReservationList() {
        LambdaQueryWrapper<SysReservation> wrapper = new LambdaQueryWrapper<>();
        Date now = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time=simpleDateFormat.format(now);
        wrapper.like(SysReservation::getTime, time);
        wrapper.eq(SysReservation::getCreateBy,SecurityUtils.getUserId());
        return this.baseMapper.selectList(wrapper);
    }

    @Override
    public SysReservationVo getToDayReservationCount() {
        SysReservationVo sysReservationVo = new SysReservationVo();
        //当前人员本月需招，查询本月招聘计划招聘发布表的专员管理数量
        YearMonth currentYearMonth = YearMonth.now();
        LambdaQueryWrapper<SysRecruitUserSubmit> wrapperUser = new LambdaQueryWrapper<>();
        wrapperUser.eq(SysRecruitUserSubmit::getUserId, SecurityUtils.getUserId());
        wrapperUser.like(SysRecruitUserSubmit::getCreateTime, currentYearMonth);
        List<SysRecruitUserSubmit> recruitUserSubmitList=sysRecruitUserSubmitMapper.selectList(wrapperUser);
        Long total=0L;
        Long commissionerCount=0L;
        Long manageCount=0L;
        for (SysRecruitUserSubmit submit : recruitUserSubmitList) {
            if(submit.getCommissioner()!=null){
                commissionerCount=commissionerCount+submit.getCommissioner();
            }
            if(submit.getManage()!=null){
                manageCount=manageCount+submit.getManage();
            }
        }
        total=commissionerCount+manageCount;
        sysReservationVo.setThisMonthNeed(total);

        //本月已招 查询花名册入职时间为这个月的
        Date now = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time=simpleDateFormat.format(now);
        RosterQuery rq = new RosterQuery();
        rq.setInviteUerId(SecurityUtils.getUserId());
        rq.setEmploymentDate(time);
        List<SysRosterVo> SysRosterList=sysRosterMapper.selectRoster(rq);
        sysReservationVo.setThisMonthAlready(SysRosterList.size());

        //今日面试
        LambdaQueryWrapper<SysReservation> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(SysReservation::getTime, time);
        wrapper.eq(SysReservation::getCreateBy,SecurityUtils.getUserId());
        List<SysReservation> ReservationList=this.baseMapper.selectList(wrapper);
        sysReservationVo.setThisToDayInterview(ReservationList.size());

        //今日未到
        List<SysReservation> notYetlist=new ArrayList<>();
        for (SysReservation sysReservation : ReservationList) {
            LambdaQueryWrapper<SysInterview> wrapperView = new LambdaQueryWrapper<>();
            wrapperView.eq(SysInterview::getPhone, sysReservation.getIphone());
            wrapperView.eq(SysInterview::getInterviewDate, sysReservation.getTime());
            SysInterview sysInterview=sysInterviewMapper.selectOne(wrapperView);
            if(sysInterview==null){
                notYetlist.add(sysReservation);
            }
        }
        sysReservationVo.setThisToDayNotYet(notYetlist.size());
        return sysReservationVo;
    }


    /**
     * 批量新增预约面试信息
     * @param reservationList
     * @return
     * @throws Exception
     */
    @Override
    public int addSysReservation(List<SysReservation> reservationList) throws Exception {
        if (Objects.isNull(reservationList)) {
            throw new Exception("至少添加一个面试者信息！");
        }
        for (SysReservation sysReservation : reservationList) {
            sysReservation.setMatters(SecurityUtils.getUsername());
            baseMapper.insert(sysReservation);
        }
        return 1;
    }
}
