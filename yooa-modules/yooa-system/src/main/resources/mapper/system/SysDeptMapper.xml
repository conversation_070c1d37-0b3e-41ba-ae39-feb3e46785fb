<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysDeptMapper">

    <sql id="selectDeptVo">
        SELECT d.dept_id,
               d.parent_id,
               d.ancestors,
               d.ancestors_names,
               d.dept_name,
               d.leader,
               d.status,
               d.sort,
               d.del_flag,
               d.create_by,
               d.create_time,
               d.update_by,
               d.update_time,
               d.dept_type,
               d.dept_level,
               d.ding_dept_id,
               d.hierarchy,
               d.lowermost,
               d.remark,
               u.nick_name AS leaderName
        FROM sys_dept d
                 LEFT JOIN sys_user AS u ON u.user_id = d.leader
    </sql>

    <select id="selectDeptList" resultType="com.yooa.system.api.domain.SysDept">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="query.deptId != null and query.deptId != 0">
            AND d.dept_id = #{query.deptId}
        </if>
        <if test="query.parentId != null and query.parentId != 0">
            AND d.parent_id = #{query.parentId}
        </if>
        <if test="query.deptName != null and query.deptName != ''">
            AND d.dept_name like concat('%', #{query.deptName}, '%')
        </if>
        <if test="query.status != null and query.status != ''">
            AND d.status = #{query.status}
        </if>
        <if test="query.deptIds != null and query.deptIds.size > 0">
            AND d.dept_id IN
            <foreach collection="query.deptIds" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.parentIdList != null and query.parentIdList.size > 0">
            AND d.parent_id IN
            <foreach collection="query.parentIdList" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.hierarchy != null and query.hierarchy != 0">
            <!-- 部门树结构层级 -->
            <if test="query.direction == 0">
                <!-- 往上查 -->
                AND d.hierarchy &lt; #{query.hierarchy}
            </if>
            <if test="query.direction == 1">
                <!-- 往下查 -->
                AND d.hierarchy > #{query.hierarchy}
            </if>
            <if test="query.direction != 0 and query.direction != 1 ">
                <!-- 查指定层级 -->
                AND d.hierarchy = #{query.hierarchy}
            </if>
        </if>
        <if test="query.deptType != null and query.deptType != 0">
            AND d.dept_type = #{query.deptType}
        </if>
        <if test="query.deptTypes != null">
            AND d.dept_type IN
            <foreach collection="query.deptTypes" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.ancestors != null and query.ancestors != 0">
            AND FIND_IN_SET(#{query.ancestors},d.ancestors)
        </if>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        order by d.parent_id, d.sort
    </select>

    <select id="selectDeptById" resultType="com.yooa.system.api.domain.SysDept">
        SELECT
            d.*,
            u.nick_name AS leaderName,
            u.user_id  AS leaderId
        FROM sys_dept d
        LEFT JOIN sys_user AS u ON u.user_id = d.leader
        where d.dept_id = #{deptId}
    </select>

    <select id="selectDeptListByRoleId" resultType="Long">
        select d.dept_id
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
        <if test="deptCheckStrictly">
            and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id =
            rd.dept_id and rd.role_id = #{roleId})
        </if>
        order by d.parent_id, d.sort
    </select>

    <select id="selectSJListByRoleId" resultType="Long">
        select d.dept_id as deptId,d.dept_name as deptName
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId} and d.parent_id="233"
        order by d.parent_id, d.sort
    </select>

    <select id="checkDeptExistUser" resultType="int">
        SELECT COUNT(1)
        FROM sys_user
        WHERE dept_id = #{deptId}
          AND del_flag = '0'
    </select>

    <select id="hasChildByDeptId" resultType="int">
        SELECT COUNT(1)
        FROM sys_dept
        WHERE del_flag = '0'
          AND parent_id = #{deptId}
        LIMIT 1
    </select>

    <select id="selectAllChildrenDeptById" resultType="com.yooa.system.api.domain.SysDept">
        SELECT *
        FROM sys_dept
        WHERE FIND_IN_SET(#{deptId}, ancestors)
    </select>

    <select id="selectChildrenDeptById" resultType="com.yooa.system.api.domain.SysDept">
        SELECT *
        FROM sys_dept
        WHERE del_flag = '0'
          AND parent_id = #{deptId}
    </select>

    <select id="selectNormalChildrenDeptById" resultType="int">
        SELECT COUNT(*)
        FROM sys_dept
        WHERE status = 0
          AND del_flag = '0'
          AND FIND_IN_SET(#{deptId}, ancestors)
    </select>

    <select id="selectAllSysDept" resultType="com.yooa.system.api.domain.SysDept">
        SELECT *
        FROM sys_dept
        WHERE status = 0
          AND del_flag = '0'
    </select>

    <select id="checkDeptNameUnique" resultType="com.yooa.system.api.domain.SysDept">
        SELECT d.dept_id,
               d.parent_id,
               d.ancestors,
               d.dept_name,
               d.leader,
               d.status,
               d.sort,
               d.del_flag,
               d.create_by,
               d.create_time,
               d.update_by,
               d.update_time,
               d.dept_type,
               d.hierarchy,
               d.lowermost,
               d.remark
        FROM sys_dept d
        WHERE dept_name = #{deptName}
          AND parent_id = #{parentId}
          AND del_flag = '0'
        LIMIT 1
    </select>

    <update id="updateDeptChildren">
        <foreach collection="deptList" item="item" index="index" separator=";" open="" close="">
            update sys_dept
            <trim prefix="SET" suffixOverrides=",">
                update_time = sysdate(),
                <if test="item.parentId != null">parent_id = #{item.parentId},</if>
                <if test="item.ancestors != null">ancestors = #{item.ancestors},</if>
                <if test="item.ancestorsNames != null">ancestors_names = #{item.ancestorsNames},</if>
                <if test="item.deptName != null">dept_name = #{item.deptName},</if>
                <if test="item.sort != null">sort = #{item.sort},</if>
                <if test="item.leader != null">leader = #{item.leader},</if>
                <if test="item.status != null">`status` = #{item.status},</if>
                <if test="item.delFlag != null">del_flag = #{item.delFlag},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>
                <if test="item.deptType != null">dept_type = #{item.deptType},</if>
                <if test="item.deptLevel != null">dept_level = #{item.deptLevel},</if>
                <if test="item.dingDeptId != null">ding_dept_id = #{item.dingDeptId},</if>
                <if test="item.hierarchy != null">hierarchy = #{item.hierarchy},</if>
                <if test="item.lowermost != null">lowermost = #{item.lowermost},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
            </trim>
            where dept_id = #{item.deptId}
        </foreach>
    </update>

    <update id="updateDeptStatusNormal">
        update sys_dept set status = '0' where dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>

</mapper>
