<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysRoleMapper">

    <resultMap type="com.yooa.system.api.domain.vo.SysRoleVo" id="SysRoleResult">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="dataScope" column="data_scope"/>
        <result property="menuCheckStrictly" column="menu_check_strictly"/>
        <result property="deptCheckStrictly" column="dept_check_strictly"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectRoleVo">
        SELECT DISTINCT r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_type,
                        r.position_type,
                        r.data_scope,
                        r.menu_check_strictly,
                        r.dept_check_strictly,
                        r.status,
                        r.sort,
                        r.del_flag,
                        r.create_time,
                        r.remark
        FROM sys_role r
                 LEFT JOIN sys_user_role ur ON ur.role_id = r.role_id
                 LEFT JOIN sys_user u ON u.user_id = ur.user_id
                 LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
    </sql>

    <select id="selectRoleList" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0'
        <if test="query.roleId != null and query.roleId != 0">
            AND r.role_id = #{query.roleId}
        </if>
        <if test="query.roleName != null and query.roleName != ''">
            AND r.role_name like concat('%', #{query.roleName}, '%')
        </if>
        <if test="query.status != null and query.status != ''">
            AND r.status = #{query.status}
        </if>
        <if test="query.roleKey != null and query.roleKey != ''">
            AND r.role_key like concat('%', #{query.roleKey}, '%')
        </if>
        <if test="query.roleType != null and query.roleType != ''">
            AND r.role_type = #{query.roleType}
        </if>
        <if test="query.positionType != null and query.positionType != ''">
            AND r.position_type = #{query.positionType}
        </if>
        <!-- 数据范围过滤 -->
        ${query.params.dataScope}
        order by r.sort
    </select>

    <select id="getRoleList" resultType="com.yooa.system.api.domain.SysRole">
        SELECT r.role_id,
        r.role_name,
        r.role_key,
        r.role_type,
        r.position_type,
        r.data_scope,
        r.menu_check_strictly,
        r.dept_check_strictly,
        r.status,
        r.sort,
        r.del_flag,
        r.create_time,
        r.remark
        FROM sys_role r
        WHERE r.del_flag = '0' AND r.role_id NOT IN(1,2,7)
        <if test="query.roleType != null and query.roleType != ''">
            AND r.role_type = #{query.roleType}
        </if>
        <if test="query.positionType != null and query.positionType != ''">
            AND r.position_type = #{query.positionType}
        </if>
        order by r.sort
    </select>

    <select id="selectRolePermissionByUserId" parameterType="Long" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and ur.user_id = #{userId}
    </select>

    <select id="selectRoleAll" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
    </select>

    <select id="selectRoleListByUserId" parameterType="Long" resultType="Long">
        SELECT r.role_id
        FROM sys_role r
                 LEFT JOIN sys_user_role ur ON ur.role_id = r.role_id
                 LEFT JOIN sys_user u ON u.user_id = ur.user_id
        WHERE u.user_id = #{userId}
    </select>

    <select id="selectRoleById" parameterType="Long" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.role_id = #{roleId}
    </select>

    <select id="selectRolesByUserName" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and u.nick_name = #{nickName}
    </select>

    <select id="checkRoleNameUnique" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.role_name=#{roleName} and r.del_flag = '0' limit 1
    </select>

    <select id="checkRoleKeyUnique" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.role_key=#{roleKey} and r.del_flag = '0' limit 1
    </select>
</mapper> 