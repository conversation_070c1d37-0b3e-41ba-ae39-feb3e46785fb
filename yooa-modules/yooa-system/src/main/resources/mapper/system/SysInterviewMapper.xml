<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysInterviewMapper">

    <select id="selectInterviewList" resultType="com.yooa.system.api.domain.vo.InterviewListVo">
        SELECT i.interview_id              AS interview_id,
               i.interview_name            AS interview_name,
               i.interview_status          AS interview_status,
               i.interview_date            AS interview_date,
               i.sex                       AS sex,
               i.channel_type              AS channel_type,
               i.position                  AS position,
               i.role_type                 AS role_type,
               i.salary_expectation        AS salary_expectation,
               i.estimate_employment_date  AS estimate_employment_date,
               i.communication_style_extra AS communication_style_extra,
               i.registration_form_url     AS registration_form_url,
               u1.user_id                  AS invite_user_id,
               u1.nick_name                AS invite_nick_name,
               u2.user_id                  AS recruit_user_id,
               u2.nick_name                AS recruit_nick_name,
               d2.dept_id                   AS recruit_user_dept_id,
               d2.dept_name                 AS recruit_user_dept_name,
               d2.ancestors_names           AS recruit_user_dept_ancestors_names,
               r.roster_id                 AS employee_id,
               u3.user_name                 AS channelName,
               r.employment_date           AS employment_date,
               r.employee_status          AS employeeStatus,
              r.employment_date             AS employmentDate
        FROM sys_interview i
                 LEFT JOIN sys_user u1 ON i.invite_user_id = u1.user_id
                 LEFT JOIN sys_user u2 ON i.recruit_user_id = u2.user_id
                 LEFT JOIN sys_user u3 on i.channel_id = u3.user_id
                 LEFT JOIN sys_dept d1 ON u1.dept_id = d1.dept_id
                 LEFT JOIN sys_dept d2 ON u2.dept_id = d2.dept_id
                 LEFT JOIN sys_roster r ON r.interview_id = i.interview_id
        <where>
            <if test="query.interviewMonth != null and query.interviewMonth != ''">
                AND i.interview_date LIKE CONCAT('%',#{query.interviewMonth},'%')
            </if>
            <if test="query.recruitUserId != null">
                AND i.recruit_user_id = #{query.recruitUserId}
            </if>
            <if test="query.inviteDeptIdList != null">
                and (u2.dept_id in
                <foreach collection="query.inviteDeptIdList" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                or
                <foreach collection="query.inviteDeptIdList" item="deptId" open="(" separator="OR" close=")">
                    FIND_IN_SET(#{deptId}, d2.ancestors )
                </foreach>)
            </if>

            <if test="query.interviewName != null and query.interviewName != ''">
                AND i.interview_name LIKE CONCAT('%',#{query.interviewName},'%')
            </if>
            <if test="query.interviewStatus != null and query.interviewStatus != ''">
                AND i.interview_status = #{query.interviewStatus}
            </if>
            <if test="query.positionType != null and query.positionType != ''">
                AND i.position_type = #{query.positionType}
            </if>
            <if test="query.roleType != null and query.roleType != ''">
                AND i.role_type = #{query.roleType}
            </if>
            <if test="query.inviteUserId != null">
                <if test="query.inviteUserId != 1 and query.inviteUserId != 972 and query.inviteUserId != 709 and query.inviteUserId != 159">
                    AND i.invite_user_id NOT IN(972,709,159)
                </if>
                <if test="query.inviteUserId == 709">
                    AND i.invite_user_id NOT IN(972,159)
                </if>
                <if test="query.inviteUserId == 159">
                    AND i.invite_user_id NOT IN(972,709)
                </if>
                <if test="query.inviteUserId == 972">
                        AND i.invite_user_id = #{query.inviteUserId}
                </if>
            </if>
            <if test="query.inviteNickName != null and query.inviteNickName != ''">
                AND u1.nick_name LIKE CONCAT('%',#{query.inviteNickName},'%')
            </if>
            <if test="query.inviteDeptId != null">
                AND (u1.dept_id = #{query.inviteDeptId} OR FIND_IN_SET(#{query.inviteDeptId},d1.ancestors))
            </if>
            <if test="query.recruitUserId != null">
                AND i.recruit_user_id = #{query.recruitUserId}
            </if>
            <if test="query.recruitUserDeptId != null">
                AND (u2.dept_id = #{query.recruitUserDeptId} OR FIND_IN_SET(#{query.recruitUserDeptId},d2.ancestors))
            </if>
            <if test="query.interviewDate != null">
                AND (i.interview_date BETWEEN #{query.interviewDate[0]} AND #{query.interviewDate[1]})
            </if>
            <if test="query.estimateEmploymentDate != null">
                AND (i.estimate_employment_date BETWEEN #{query.estimateEmploymentDate[0]} AND #{query.estimateEmploymentDate[1]})
            </if>
            <if test="query.employmentDay != null">
                <if test="query.employmentDay[0] != null">
                    AND TIMESTAMPDIFF(DAY,r.employment_date,NOW()) >= #{query.employmentDay[0]}
                </if>
                <if test="query.employmentDay[1] != null">
                    AND TIMESTAMPDIFF(DAY,r.employment_date,NOW()) &lt;= #{query.employmentDay[1]}
                </if>
            </if>
        </where>
        ORDER BY i.create_time DESC
    </select>

    <select id="selectInterview" resultType="com.yooa.system.api.domain.vo.InterviewListVo">
        select * from sys_interview
        <where>
            <if test="query.estimateEmploymentDate != null">
                AND estimate_employment_date LIKE CONCAT('%',#{query.estimateEmploymentDate},'%')
            </if>
            <if test="query.interviewStatus != null">
                AND interview_status = #{interviewStatus}
            </if>
            <if test="query.inviteUserId != null">
                AND invite_user_id = #{query.inviteUserId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectInterviewById" resultType="com.yooa.system.api.domain.vo.InterviewVo">
        SELECT i.*, u1.nick_name AS invite_nick_name, u2.user_name channelName
        FROM sys_interview i
                 LEFT JOIN sys_user u1 ON i.invite_user_id = u1.user_id
                 LEFT JOIN sys_user u2 ON i.channel_id = u2.user_id
        WHERE i.interview_id = #{interviewId}
    </select>

    <select id="selectByIdCardNumber" resultType="com.yooa.system.api.domain.SysInterview">
        SELECT * FROM sys_interview WHERE id_card_number = #{idCardNumber}
    </select>


    <select id="selectThisMonthList" resultType="com.yooa.system.api.domain.SysRoster">
        SELECT * FROM  sys_roster r
        Left join sys_interview i on r.interview_id=i.interview_id
        <where>
        <if test="query.employmentDate != null and query.employmentDate != ''">
           and r.employment_date like concat('%', #{query.employmentDate}, '%')
        </if>
        <if test="query.inviteUserId != null">
            and i.invite_user_id = #{query.inviteUserId}
        </if>
        <if test="query.interviewRole != null and query.interviewRole != '' and query.interviewRole == 5">
            and i.interview_role = #{query.interviewRole}
        </if>
        <if test="query.interviewRole != null and query.interviewRole != '' and query.interviewRole != 5">
            and i.interview_role in(1,2,3,4)
        </if>
        </where>
    </select>

</mapper>
