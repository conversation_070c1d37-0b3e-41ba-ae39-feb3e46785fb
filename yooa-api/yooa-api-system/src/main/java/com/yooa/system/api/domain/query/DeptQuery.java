package com.yooa.system.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.*;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptQuery extends QueryEntity {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门状态（0正常 1停用）
     */
    private String status;

    /**
     * 部门id集
     */
    private List<Long> deptIds;

    /**
     * 父部门ID集合
     */
    private List<Long> parentIdList;
    /**
     * 部门层级
     */
    private Integer hierarchy;

    /**
     * 根据部门层级向上或向下查为空就查指定层(0:上(大于)/1:下(小于))
     */
    private Integer direction;

    /**
     * 部门类型
     */
    private Integer deptType;

    /**
     * 部门类型数组
     */
    private Integer[] deptTypes;

    /**
     * 祖籍列表
     */
    private Long ancestors;
}
