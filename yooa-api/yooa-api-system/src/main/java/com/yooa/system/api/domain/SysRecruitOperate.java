package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 招聘人员分配表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRecruitOperate extends BaseEntity {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long Id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 计划ID
     */
    private Long recruitId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 所需部门
     */
    private String dept;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 专员数量
     */
    private Long commissioner;

    /**
     * 管理数量
     */
    private Long manage;

    /**
     * 操作年月
     */
    private String operateTime;

    /**
     * 操作具体时间
     */
    private String operateDate;
}
