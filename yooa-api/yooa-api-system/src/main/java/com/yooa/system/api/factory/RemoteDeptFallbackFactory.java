package com.yooa.system.api.factory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.R;
import com.yooa.system.api.RemoteDeptService;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.dto.QueryPage;
import com.yooa.system.api.domain.query.DeptQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 部门服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteDeptFallbackFactory implements FallbackFactory<RemoteDeptService> {
    @Override
    public RemoteDeptService create(Throwable throwable) {

        log.error("部门服务调用失败:{}", throwable.getMessage());

        return new RemoteDeptService() {
            @Override
            public R<List<SysDept>> getDeptList(DeptQuery query, String source) {
                return R.fail("查询部门信息失败:" + throwable.getMessage());
            }

            @Override
            public R<Page<SysDept>> getPageDeptList(QueryPage queryPage, String source) {
                return R.fail("查询分页部门信息失败:" + throwable.getMessage());
            }
        };
    }
}
