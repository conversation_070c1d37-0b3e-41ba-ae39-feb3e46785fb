package com.yooa.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.SysMenu;
import com.yooa.system.api.domain.SysRoster;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Data
public class TreeSelectVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 层级
     */
    private Integer hierarchy;

    /**
     * 级别
     */
    private String level;

    /**
     * 当月部门入职集合
     */
    private List<SysRoster> rosterEntryLis;

    /**
     * 员工入职数量
     */
    private int commissionerEntryCount;

    /**
     * 管理入职数量
     */
    private int manageEntryCount;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelectVo> children;

    public TreeSelectVo() {

    }

    public TreeSelectVo(SysDept dept) {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.hierarchy = dept.getHierarchy();
        this.level = dept.getDeptLevel();
        this.children = dept.getChildren().stream().map(TreeSelectVo::new).collect(Collectors.toList());
    }

    public TreeSelectVo(SysMenu menu) {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelectVo::new).collect(Collectors.toList());
    }
}
