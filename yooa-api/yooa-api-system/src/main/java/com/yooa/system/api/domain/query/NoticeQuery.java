package com.yooa.system.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeQuery extends QueryEntity {

    /**
     * 通知类型
     */
    private String noticeType;

    /**
     * 发送类型
     */
    private String sendType;

    /**
     * 通知状态
     */
    private String status;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户id查询控制参数 （1我创建的 2我接收的 3我创建和我接收的）
     */
    private Integer ascription = 3;

    private LocalDate createDate;
}
