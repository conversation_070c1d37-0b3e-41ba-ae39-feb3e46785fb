package com.yooa.system.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class RosterQuery extends QueryEntity {

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 学历类型
     */
    private String educationalType;

    /**
     * 职位类型
     */
    private String positionType;

    /**
     * 岗位职级
     */
    private String postRank;

    /**
     * 员工类型
     */
    private String employeeType;

    /**
     * 员工角色
     */
    private String employeeRole;

    /**
     * 员工状态
     */
    private String employeeStatus;

    /**
     * 员工部门
     */
    private Long deptId;

    /**
     * 部门类型
     */
    private String deptType;

    /**
     * 离职日期
     */
    private String resignationDate;

    /**
     * 计划转正日期
     */
    private String planFormalDate;

    /**
     * 入职日期
     */
    private String employmentDate;

    /**
     * 邀约用户ID
     */
    private Long inviteUerId;
}
