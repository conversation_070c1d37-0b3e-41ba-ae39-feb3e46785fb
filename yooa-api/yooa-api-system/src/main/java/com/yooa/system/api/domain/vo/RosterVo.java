package com.yooa.system.api.domain.vo;

import com.yooa.common.sensitive.annotation.Sensitive;
import com.yooa.common.sensitive.enums.DesensitizedType;
import com.yooa.system.api.domain.SysRoster;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class RosterVo extends SysRoster {

    /**
     * 员工部门ID
     */
    private Long deptId;

    /**
     * 员工部门名称
     */
    private String deptName;

    /**
     * 员工部门父名称
     */
    private String deptAncestorsNames;

    /**
     * 推荐用户
     */
    private String recommenUserName;

    /**
     * 直属领导
     */
    private String leaderUserName;

    /**
     * 脱敏身份证号码
     */
    @Sensitive(desensitizedType = DesensitizedType.ID_CARD)
    private String idCardNumber;

    /**
     * 脱敏银行卡号
     */
    @Sensitive(desensitizedType = DesensitizedType.BANK_CARD)
    private String bankCardNumber;

    /**
     * 脱敏支付宝账号
     */
    @Sensitive(desensitizedType = DesensitizedType.ALi_PAY)
    private String alipayAccount;

}

