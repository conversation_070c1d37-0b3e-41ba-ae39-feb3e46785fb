package com.yooa.system.api.domain.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


@Data
@EqualsAndHashCode
public class NoticeSaveDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "通知标题不能为空")
    @Size(min = 0, max = 50, message = "通知标题不能超过50个字符")
    private String noticeTitle;

    @NotBlank(message = "通知类型不能为空")
    private String noticeType;

    @NotBlank(message = "通知内容不能为空")
    private String noticeContent;

    @Size(min = 1, message = "范围（部门集）不能为空")
    private List<Long> deptIds;

    @NotBlank(message = "发送类型不能为空")
    private String sendType;

    private LocalDateTime sendTime;

    /**
     * 保存类型（临时定义用于分别是保存还是提交）（1保存 2提交）
     */
    @NotNull(message = "保存类型不能为空")
    private String saveType;
}
