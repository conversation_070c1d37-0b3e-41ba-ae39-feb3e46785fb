package com.yooa.cmf.api.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 */
@Data
public class UserJoinVo {

    /**
     * ID
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer uid;

    /**
     * 用户昵称
     */
    private String uname;

    /**
     * 主播id
     */
    private Integer liveid;

    /**
     * 主播昵称
     */
    private String liveName;

    /**
     * 接收人id
     */
    private Integer operateid;

    /**
     * 接收人姓名
     */
    private String operatename;

    /**
     * 总充值
     */
    private Double totalUp;

    /**
     * 接收时间
     */
    private LocalDateTime uptime;

    /**
     * 最后登入时间
     */
    private LocalDateTime lastOpenTime;

}
