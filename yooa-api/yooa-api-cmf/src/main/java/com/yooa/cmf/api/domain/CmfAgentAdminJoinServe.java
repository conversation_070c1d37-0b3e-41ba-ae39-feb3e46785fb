package com.yooa.cmf.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 推广交接客服记录表
 */
@TableName(value = "cmf_agent_admin_join_serve")
@Data
public class CmfAgentAdminJoinServe implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 推广id
     */
    @TableField(value = "adminid")
    private Integer adminid;
    /**
     * 客服id
     */
    @TableField(value = "serveid")
    private Integer serveid;
    /**
     * 用户id
     */
    @TableField(value = "uid")
    private Integer uid;
    /**
     * 0:代接收 1:接收 2:拒绝接收
     */
    @TableField(value = "state")
    private Integer state;
    /**
     * 交接记录id
     */
    @TableField(value = "joinid")
    private Integer joinid;
    /**
     * 交接时间
     */
    @TableField(value = "addtime")
    private Date addtime;
    /**
     * 接收时间
     */
    @TableField(value = "uptime")
    private Date uptime;
    /**
     * 拒绝备注
     */
    @TableField(value = "msg")
    private String msg;
    /**
     * 图片  需求改了 后面加的
     */
    @TableField(value = "img")
    private String img;
    /**
     * 备注信息  需求改了 后面加的
     */
    @TableField(value = "message")
    private String message;
    /**
     * 反馈意见 0：未未反馈 1：反馈
     */
    @TableField(value = "feedback")
    private Integer feedback;
    /**
     * 反馈消息
     */
    @TableField(value = "feedback_msg")
    private String feedbackMsg;
}