package com.yooa.crm.api.domain;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 用户观看直播时间(CrmCustomerViewLiveTime)实体类
 *
 * <AUTHOR>
 * @since 2025-03-19 18:09:56
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("crm_customer_view_live_time")
public class CrmCustomerViewLiveTime implements Serializable {
    private static final long serialVersionUID = 290444345249817326L;


    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户进入时间
     */
    private LocalDateTime viewTime;


    /**
     * 观看日期
     */
    private LocalDate viewDate;


    /**
     * 观看时间 (秒)
     */
    private Long enterTime;


}
