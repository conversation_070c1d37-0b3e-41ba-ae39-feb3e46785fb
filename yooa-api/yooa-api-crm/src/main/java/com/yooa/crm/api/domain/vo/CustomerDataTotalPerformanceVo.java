package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/2 18:07
 * @Description:
 */
@Data
public class CustomerDataTotalPerformanceVo {

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 客户昵称
     */
    private String customerNickName;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 本月充值
     */
    private BigDecimal monthRechargeAmount;

    /**
     * 总充值
     */
    private BigDecimal totalRechargeAmount;

    /**
     * 所属推广
     */
    private String extendName;

    /**
     * 推广团队
     */
    private String extendDeptName;

    /**
     * 推广祖级列表
     */
    private String extendAncestors;

    /**
     * 所属客服
     */
    private String serveName;

    /**
     * 客服团队
     */
    private String serveDeptName;

    /**
     * 客服祖级列表
     */
    private String serveAncestors;

    /**
     * 运营主播信息
     */
    private List<AnchorOperateVo> anchorOperate;


}
