package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> xh
 * @Date: 2025/4/11 9:55
 * @Description:
 */
@Data
public class AnchorQuery extends QueryEntity {

    /**
     *  部门ID
     */
    private Long deptId;

    /**
     * 语言（1中文 2英文）
     */
    private List<Integer> language;

    /**
     * 地区
     */
    private List<String> region;

    /**
     * 风格
     */
    private List<Integer> anchorStyle;

    /**
     * 主播类型（1线上 2线下 3兼职）
     */
    private List<Integer> anchorType;

    /**
     * 主播状态 (0正常、1未开、2休播、3自离、4淘汰 5待停播)
     */
    private List<Integer> status;

    /**
     * 开播时间(开始)
     */
    private LocalDateTime liveStartTime;

    /**
     * 开播时间(结束)
     */
    private LocalDateTime liveEndTime;

    /**
     * 根据pd账号或姓名或花名搜索
     */
    private String anchorIdOrNameQuery;

    /**
     * 性别
     */
    private Integer sex;

}
