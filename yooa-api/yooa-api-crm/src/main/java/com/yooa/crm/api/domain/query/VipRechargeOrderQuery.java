package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/2 13:42
 * @Description:
 */
@Data
public class VipRechargeOrderQuery extends QueryEntity {

    /**
     * 充值最低金额
     */
    private BigDecimal minAmount;

    /**
     * 充值最高金额
     */
    private BigDecimal maxAmount;

    /**
     * 根据客户id 订单号 第三方订单号 查询
     */
    private String queryId;


}
