package com.yooa.crm.api.domain.vo;

import lombok.Data;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/5 16:15
 * @Description:
 */
@Data
public class AnchorInfoDetailVo {


    /**
     * 主播账号id
     */
    private Long anchorAccountId;

    /**
     * 网名
     */
    private String anchorName;

    /**
     * 主播账号
     */
    private String anchorAccount;

    /**
     * 主播风格（1搞笑 2幽默）
     */
  //  private String anchorStyle;

    /**
     * 状态（0正常 1注销）
     */
   // private String status;

    /**
     * 注册时间
     */
    private String registerTime;

    /**
     * 备注
     */
  //  private String remark;

    /**
     * 今日直播时长
     */
    private String todayLiveHour;

    /**
     * 本月直播时长
     */
    private String monthLiveHor;

    /**
     * 总共直播时长
     */
    private String totalLiveHor;
}

