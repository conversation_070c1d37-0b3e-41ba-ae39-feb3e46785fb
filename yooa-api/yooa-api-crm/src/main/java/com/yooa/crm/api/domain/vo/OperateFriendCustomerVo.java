package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.core.annotation.Excels;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 运营客户列表返回类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperateFriendCustomerVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @Excel(name = "客户ID")
    private Long customerId;

    /**
     * 好友id
     */
    @Excel(name = "好友ID")
    private Long friendId;

    /**
     * 好友名称
     */
    @Excel(name = "客户名")
    private String friendName;

    /**
     * 首次打赏粉
     */
    @Excel(name = "首次打赏粉")
    private Integer fansUpReward;

    /**
     * 首次充值粉
     */
    @Excel(name = "首次充值粉")
    private Integer fansUpRecharge;

    /**
     * 英文一百粉
     */
    @Excel(name = "英文一百粉")
    private Integer fans1h;

    /**
     * 两百粉
     */
    @Excel(name = "两百粉")
    private Integer fans2h;

    /**
     * 五百粉
     */
    @Excel(name = "五百粉")
    private Integer fans5h;

    /**
     * 五千粉
     */
    @Excel(name = "五千粉")
    private Integer fans5k;

    /**
     * 五万粉
     */
    @Excel(name = "五万粉")
    private Integer fans5w;

    /**
     * 十万粉
     */
    @Excel(name = "十万粉")
    private Integer fans10w;

    private Long userId;

    /**
     * 接收时间
     */
    @Excel(name = "接收时间")
    private LocalDateTime receiveTime;

    /**
     * 客户流失时间
     */
    @Excel(name = "客户解绑时间")
    private LocalDateTime loseTime;

    /**
     * 客户状态（1首次、2多次）
     */
    @Excel(name = "状态")
    private String status;

    /**
     * 客户注册时间
     */
    @Excel(name = "注册时间")
    private LocalDateTime createTime;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 最后登入时间
     */
    @Excel(name = "最后登入时间")
    private LocalDateTime lastLoginTime;

    /**
     * 客户总打赏
     */
    @Excel(name = "总打赏")
    private BigDecimal totalRecharge;

    /**
     * 客户实际打赏
     */
    @Excel(name = "实际打赏")
    private BigDecimal actualRecharge;

    /**
     * 推广信息
     */
    @Excels({
            @Excel(name = "推广负责人", targetAttr = "nickName", type = Excel.Type.EXPORT),
            @Excel(name = "推广上级部门", targetAttr = "ancestorsNames", type = Excel.Type.EXPORT),
            @Excel(name = "推广部门", targetAttr = "deptName", type = Excel.Type.EXPORT)
    })
    private ExtendVo extend;

    /**
     * 客服信息
     */
    @Excels({
            @Excel(name = "客服负责人", targetAttr = "nickName", type = Excel.Type.EXPORT),
    })
    private ServeVo serve;

    /**
     * 运营主播信息
     */
    @Excels({
            @Excel(name = "主播", targetAttr = "anchorName", type = Excel.Type.EXPORT),
            @Excel(name = "主播语言", targetAttr = "anchorLanguage", type = Excel.Type.EXPORT, readConverterExp = "1=中文,2=英文"),
            @Excel(name = "主播性别", targetAttr = "anchorSex", type = Excel.Type.EXPORT, readConverterExp = "0=男,1=女"),
            @Excel(name = "运营上级部门", targetAttr = "ancestorsNames", type = Excel.Type.EXPORT),
            @Excel(name = "运营部门", targetAttr = "deptName", type = Excel.Type.EXPORT),
            @Excel(name = "运营负责人", targetAttr = "nickName", type = Excel.Type.EXPORT)
    })
    private AnchorOperateVo anchorOperate;
}
