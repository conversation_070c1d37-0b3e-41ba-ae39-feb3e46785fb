package com.yooa.crm.api.domain.dto;

import com.yooa.crm.api.domain.CrmCost;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
public class TKCostEditDto extends CrmCost {

    @NotNull(message = "成本ID不能为空")
    private Long costId;

    @NotNull(message = "好友单价不能为空")
    private BigDecimal tkFriendPrice;

}
