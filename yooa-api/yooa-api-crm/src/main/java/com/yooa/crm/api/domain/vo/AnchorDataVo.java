package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xh
 * @Date: 2025/6/18 14:15
 * @Description:
 */
@Data
public class AnchorDataVo {

    @Excel(name = "主播id")
    private Long anchorId;

    /**
     * 首充转换率
     */
    @Excel(name = "首充转换率")
    private Double firstChargeRate;

    /**
     * 交接粉丝
     */
    @Excel(name = "交接粉丝")
    private Integer joinFans;

    /**
     * 交接粉丝打赏率
     */
    @Excel(name = "交接粉丝打赏率")
    private Double joinFansRate;

    /**
     * 直播时长
     */
    @Excel(name = "直播时长")
    private Double liveHours;

    /**
     * 打赏
     */
    @Excel(name = "交接粉丝打赏")
    private BigDecimal reward;

    /**
     * 打赏率
     */
    @Excel(name = "打赏率")
    private Double rewardRate;

    /**
     * 总打赏
     */
    @Excel(name = "总打赏")
    private BigDecimal totalReward;

    /**
     * 首充
     */
    @Excel(name = "首充")
    private Integer firstCharge;

    @Excel(name = "直播状态", readConverterExp = "1=开播中,2=未开播,3=已开播")
    private Integer liveStatus;

}
