package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/21 16:15
 * @Description:
 */
@Data
public class CustomerRewardQuery extends QueryEntity {

    /**
     * 好友id
     */
    @NotNull(message = "好友id不能为空")
    private Long friendId;


    /**
     * 最低打赏金额
     */
    private BigDecimal minRewardAmount;

    /**
     * 最高打赏金额
     */
    private BigDecimal maxRewardAmount;
    /**
     * 打赏开始时间
     */
    private LocalDateTime startTime;

    /**
     * 打赏结束时间
     */
    private LocalDateTime endTime;

    /**
     * 根据 主播姓名 主播id查询
     */
 //   private String nameOrIdQuery;

    /**
     * 主播账号id
     */
    private Long anchorAccountId;

    /**
     * 根据主播姓名 搜索
     */
    private String queryId;

    // 收支行为
    private String action;

    private List<Long> customerIdList;

    private List<Long> anchorIdList;

}
