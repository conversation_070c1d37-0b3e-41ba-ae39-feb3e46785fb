package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> xh
 * @Date: 2025/4/18 16:20
 * @Description:
 */
@Data
public class CustomerJoinAnchorQuery extends QueryEntity {

    /**
     * 大小号 0:大号 1:小号
     */
    private Integer accountType;

    /**
     * 开始日期
     */
    private LocalDateTime receiveBeginTime;

    /**
     * 结束日期
     */
    private LocalDateTime receiveEndTime;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 客户名称或ID查询
     */
    private String customerNameOrIdQuery;

    /**
     * 语种
     */
    private Integer language;

    /**
     * 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
     */
    private Integer fansType;

    /**
     * 绑定类型 0:未绑定好友 1:已绑定好友
     */
    private String bindType;

    /**
     * 数据是否有效 0:有效 1:无效
     */
    private Integer isItValid;
}
