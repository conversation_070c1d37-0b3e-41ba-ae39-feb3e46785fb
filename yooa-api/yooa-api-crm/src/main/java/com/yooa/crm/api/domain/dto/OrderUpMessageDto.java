package com.yooa.crm.api.domain.dto;

import com.yooa.crm.api.domain.CrmCustomerOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderUpMessageDto {

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 消费者当月充值
     */
    private BigDecimal monthUp = BigDecimal.ZERO;

    /**
     * 消费者总充值
     */
    private BigDecimal totalUp = BigDecimal.ZERO;

    /**
     * 消费者当月充值笔数
     */
    private Integer monthUpNumber = 0;

    /**
     * 消费者总充值笔数
     */
    private Integer totalUpNumber = 0;

    /**
     * 订单列表
     */
    private List<CrmCustomerOrder> orderList;
}
