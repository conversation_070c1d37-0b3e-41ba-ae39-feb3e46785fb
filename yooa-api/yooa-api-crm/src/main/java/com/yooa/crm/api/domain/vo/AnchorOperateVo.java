package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AnchorOperateVo {

    /**
     * PD运营id
     */
    private Long pyOperateId;

    /**
     * 运营id
     */
    private Long userId;

    /**
     * 运营名称
     */
    private String userName;

    /**
     * 运营昵称
     */
    private String nickName;

    /**
     * 运营部门id
     */
    private Long deptId;

    /**
     * 运营部门
     */
    private String deptName;

    /**
     * 运营父部门
     */
    private String ancestorsNames;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 主播名称
     */
    private String anchorName;

    /**
     * 主播语言（1中文 2英文）
     */
    private Long anchorLanguage;

    /**
     * 主播性别（0男 1女）
     */
    private Long anchorSex;

    /**
     * 接手时间
     */
    private LocalDateTime receiveTime;

    /**
     * 脱手时间
     */
    private LocalDateTime loseTime;

    /**
     * 接手方式 (1: 公海 2: 自招)
     */
    private Integer joinType;

    /**
     * 客户ID
     */
    private Long customerId;
}
