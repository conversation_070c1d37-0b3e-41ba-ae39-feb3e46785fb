package com.yooa.crm.api.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/21 9:44
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerRewardInfoVo extends AnchorRewardInfoVo {

    /**
     * 打赏时间
     */
   private LocalDateTime rewardTime;

    /**
     * 主播网名
     */
   private String anchorNickName;

    /**
     * 打赏金额
     */
   private BigDecimal rewardAmt;

    /**
     * 收支行为 (字典)
     */
   private String action;

    /**
     * 礼物
     */
   private String giftName;
}
