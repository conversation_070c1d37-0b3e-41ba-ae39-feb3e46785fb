package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/2 13:42
 * @Description:
 */
@Data
public class VipRechargeOrderVo {

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 第三方订单号
     */
    private String thirdOrderNo;

    /**
     * 创建时间
     */
    private LocalDateTime orderTime;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付方式(字典)
     */
    private Integer paymentType;

    /**
     * 客服名字
     */
    private String serveName;

    /**
     * 客服部门名字
     */
    private String serveDeptName;

    /**
     * 父部门
     */
    private String serveAncestorsNames;
}
