package com.yooa.crm.api.domain.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 */
@Data
public class CustomerStatisticsDto implements Serializable {

    /**
     * 地区(字典)
     */
    private Integer district;

    /**
     * 分组时间(0:月、1:日)
     */
    private Integer groupType;

    /**
     * 时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    /**
     * 时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;


}
