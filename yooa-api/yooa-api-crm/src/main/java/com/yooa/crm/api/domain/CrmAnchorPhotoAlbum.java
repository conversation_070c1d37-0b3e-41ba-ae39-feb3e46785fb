package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主播相册
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CrmAnchorPhotoAlbum extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 相册地址
     */
    private String url;

    /**
     * 类型（1图片 2视频）
     */
    private String type;

}