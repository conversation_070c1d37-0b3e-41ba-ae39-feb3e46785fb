package com.yooa.external.api.factory;

import com.yooa.common.core.domain.R;
import com.yooa.external.api.RemoteDingTalkUserService;
import com.yooa.external.api.response.DingTalkUserLeaveInfoRes;
import com.yooa.external.api.response.DingTalkUserLeaveListRes;
import com.yooa.external.api.response.DingTalkUserNormalListRes;
import com.yooa.external.api.response.DingTalkUserRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 钉钉用户 - 远程调用服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteDingTalkUserFallbackFactory implements FallbackFactory<RemoteDingTalkUserService> {
    @Override
    public RemoteDingTalkUserService create(Throwable throwable) {

        log.error("钉钉服务调用失败：{}", throwable.getMessage());

        return new RemoteDingTalkUserService() {

            @Override
            public R<DingTalkUserRes> getByUserId(String userId, String source) {
                return R.fail("钉钉服务调用失败：" + throwable.getMessage());
            }

            @Override
            public R<List<DingTalkUserRes>> listByDeptId(Long userId, String source) {
                return R.fail("钉钉服务调用失败：" + throwable.getMessage());
            }

            @Override
            public R<DingTalkUserNormalListRes> listByNormal(String status, Long cursor, Long size, String source) {
                return R.fail("钉钉服务调用失败：" + throwable.getMessage());
            }

            @Override
            public R<DingTalkUserLeaveListRes> listByLeave(Long cursor, Integer size, String source) {
                return R.fail("钉钉服务调用失败：" + throwable.getMessage());
            }

            @Override
            public R<DingTalkUserLeaveInfoRes> getLeaveInfo(String userId, String source) {
                return R.fail("钉钉服务调用失败：" + throwable.getMessage());
            }
        };
    }
}
