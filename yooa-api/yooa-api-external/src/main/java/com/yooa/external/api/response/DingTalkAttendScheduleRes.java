package com.yooa.external.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤班次
 *
 * <AUTHOR>
 */
@Data
public class DingTalkAttendScheduleRes implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 班次id
     */
    private Long classId;
    /**
     * 考勤组ID
     */
    private Long groupId;
    /**
     * 排班时间
     */
    private Date planDate;
    /**
     * 计划打卡时间
     */
    private Date planCheckTime;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 类型 Onduty OffDuty
     */
    private String checkType;

}
