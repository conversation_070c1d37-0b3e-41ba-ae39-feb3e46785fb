package com.yooa.external.api;


import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.external.api.factory.RemoteDingTalkUserFallbackFactory;
import com.yooa.external.api.response.DingTalkUserLeaveInfoRes;
import com.yooa.external.api.response.DingTalkUserLeaveListRes;
import com.yooa.external.api.response.DingTalkUserNormalListRes;
import com.yooa.external.api.response.DingTalkUserRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 钉钉用户 - 远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDingTalkUserService", value = ServiceNameConstants.EXTERNAL_SERVICE, fallbackFactory = RemoteDingTalkUserFallbackFactory.class)
public interface RemoteDingTalkUserService {


    /**
     * 获取钉钉用户详情
     *
     * @param source 请求来源
     * @return 钉钉用户详情
     */
    @GetMapping("/ding-talk/user/{userId}")
    R<DingTalkUserRes> getByUserId(@PathVariable("userId") String userId,
            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取钉钉部门下的用户详情列表
     *
     * @param source 请求来源
     * @return 钉钉用户详情列表
     */
    @GetMapping("/ding-talk/user/list/{deptId}")
    R<List<DingTalkUserRes>> listByDeptId(@PathVariable("deptId") Long deptId,
            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取钉钉在职员工列表
     *
     * @param status 在职员工状态筛选，可以查询多个状态，不同状态之间使用英文逗号分隔
     *               <p>
     *               2：试用期
     *               <p>
     *               3：正式
     *               <p>
     *               5：待离职
     *               <p>
     *               -1：无状态
     * @param cursor 分页游标，从0开始，根据返回结果里的next_cursor是否为空来判断是否还有下一页，且再次调用时offset设置成next_cursor的值
     * @param size   分页大小，最大50
     * @param source 请求来源
     * @return 钉钉在职员工列表
     */
    @GetMapping("/ding-talk/user/normal-list")
    R<DingTalkUserNormalListRes> listByNormal(@RequestParam("status") String status,
            @RequestParam("cursor") Long cursor,
            @RequestParam("size") Long size,
            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取钉钉离职员工列表
     *
     * @param cursor 分页查询的游标，如果是首次查询，该参数传0或者不传，如果是非首次查询，该参数传上次调用时返回的nextToken
     * @param size   每页条目数，默认值30，最大值50
     * @param source 请求来源
     * @return 钉钉离职员工列表
     */
    @GetMapping("/ding-talk/user/leave-list")
    R<DingTalkUserLeaveListRes> listByLeave(@RequestParam("cursor") Long cursor,
            @RequestParam("size") Integer size,
            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取钉钉离职员工信息
     *
     * @param userId 钉钉用户id
     * @param source 请求来源
     * @return 钉钉离职员工信息
     */
    @GetMapping("/ding-talk/user/leave-info/{userId}")
    R<DingTalkUserLeaveInfoRes> getLeaveInfo(@PathVariable("userId") String userId,
            @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
