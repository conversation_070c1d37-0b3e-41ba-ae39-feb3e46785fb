package com.yooa.extend.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/7 下午1:37
 */
@Data
public class IndexAllVo {

    /**
     * 好友
     */
    private Integer friend;

    /**
     * 注册
     */
    private Integer register;

    /**
     * 优质
     */
    private Integer qualityUsers;

    /**
     * 接收数
     */
    private Integer receive;

    /**
     * 接收数vip/接粉数
     */
    private Integer receiveVip;

    /**
     * 首充
     */
    private Integer firstCharge;

    /**
     * 汇总业绩
     */
    private BigDecimal realProfit;

    /**
     * 充值用户数量
     */
    private Integer chargeUser;

    /**
     * 200粉
     */
    private Integer fans2h;

    /**
     * 500粉
     */
    private Integer fans5h;

    /**
     * 5k粉
     */
    private Integer fans5k;

    /**
     * 5w粉
     */
    private Integer fans5w;

    // 人数
    private Long peopleNumber;
}
