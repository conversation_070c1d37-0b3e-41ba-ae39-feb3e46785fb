package com.yooa.extend.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 */
@Data
public class PeopleRankingVo {

    // 完成字段名
    private String fieldsName;

    // 字段属性的值
    private BigDecimal value;

    // oa用户ID
    private Long id;

    // 用户名
    private String name;

    // 部门名
    private String deptName;

    // 提升比例
    private String upScale;

    // 对比值
    private BigDecimal upValue;

    // 排名
    private Integer ranking;

}
