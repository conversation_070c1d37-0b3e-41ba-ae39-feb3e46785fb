package com.yooa.extend.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/14 9:51
 * @Description:
 */
@Data
public class OperateDetailVo {
    /**
     * 用户ID/部门ID
     */
    private Long id;

    /**
     * 200粉
     */
    private Long fans2h;
    /**
     * 5000粉
     */
    private Long fans5k;
    /**
     * 50000粉
     */
    private Long fans5w;

    /**
     * 50000粉
     */
    private Long fans10w;
    /**
     * 首充
     */
    private Long firstChargeNum;
    /**
     * 交接打赏
     */
    private Long handoverRewardNum;
    /**
     * 交接
     */
    private Long handoverNum;
    /**
     * 开播数
     */
    private Long sumBeginToShow;
    /**
     * 休播数
     */
    private Long sumOffAirBroadcast;
    /**
     * 停播数
     */
    private Long sumCeaseBroadcasting;

    /**
     * 待停播
     */
    private Long toBeDiscontinued;
    /**
     * 总打赏数
     */
    private Long rewardNum;
    /**
     * 打赏业绩
     */
    private BigDecimal rewardAmount;
    /**
     * PD用户ID集
     */
    private String pdUserIds;

    /**
     * 接粉打赏率
     */
    @Excel(name = "接粉打赏率")
    private BigDecimal handoverRewardRate;

    /**
     * 接粉手冲率
     */
    @Excel(name = "接粉手冲率")
    private BigDecimal handoverFirstChargeRate;


}
