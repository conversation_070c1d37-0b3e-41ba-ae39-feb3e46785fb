package com.yooa.extend.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/12 14:22
 * @Description:
 */
@Data
public class OperateBusinessQuery extends QueryEntity {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 员工状态(字典)
     */
    private String status;

    /**
     * 搜索条件开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 名称
     */
    private String name;


}
