package com.yooa.extend.api.domain.utils;

import com.yooa.extend.api.domain.vo.WeeksListVo;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Service
public class DateWeeksList {

    /**
     * date   时间
     */
    public static List<WeeksListVo> production(LocalDate date) {
        List<WeeksListVo> weeksListVos = new ArrayList<>();       // 最总返回

        // 获取当前月份的第一天
        LocalDate firstDayOfMonth = date.withDayOfMonth(1);
        // 获取当前月份的最后一天
        LocalDate lastDayOfMonth = date.withDayOfMonth(date.lengthOfMonth());

        // 初始化第一个星期的起始日期为当月第一天
        LocalDate startOfWeek = firstDayOfMonth;
        // 找到第一个星期的结束日期
        LocalDate endOfWeek = startOfWeek.with(DayOfWeek.SUNDAY);
        // 添加第一个星期的区间范围
        WeeksListVo weeksVo = new WeeksListVo();
        weeksVo.setWeeksNumber(1);
        weeksVo.setBeginDate(startOfWeek);
        weeksVo.setEndDate(endOfWeek);
        weeksListVos.add(weeksVo);

        Integer number = 1;         // 第几周
        // 循环直到到达当月最后一天
        while (endOfWeek.isBefore(lastDayOfMonth)) {
            number++;
            // 下一个星期的起始日期是上一个星期的结束日期的下一天
            startOfWeek = endOfWeek.plusDays(1);
            // 下一个星期的结束日期是下一个星期的起始日期加上6天，即星期天
            endOfWeek = startOfWeek.with(DayOfWeek.SUNDAY);
            // 如果下一个星期的结束日期超出了当月最后一天，则将其调整为当月最后一天
            if (endOfWeek.isAfter(lastDayOfMonth)) {
                endOfWeek = lastDayOfMonth;
            }
            // 添加下一个星期的区间范围
            WeeksListVo weeksVo1 = new WeeksListVo();
            weeksVo1.setWeeksNumber(number);
            weeksVo1.setBeginDate(startOfWeek);
            weeksVo1.setEndDate(endOfWeek);
            weeksListVos.add(weeksVo1);
        }

        return weeksListVos;
    }

}
