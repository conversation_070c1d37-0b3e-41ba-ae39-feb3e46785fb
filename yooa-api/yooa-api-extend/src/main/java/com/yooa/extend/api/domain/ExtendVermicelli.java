package com.yooa.extend.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yooa.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 完成的粉丝数表
 *
 * @TableName extend_vermicelli
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtendVermicelli implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 充值粉丝类型(0:200粉、1:500粉、2:5k粉、3:5w粉、4:tk粉、5:首充粉、6:10w粉、7:40w粉、8:100w粉)
     */
    @Excel(name = "类别", sort = 5, readConverterExp = "0=200粉,1=500粉,2=5k粉,3=5w粉,4=TK粉,5=首充粉,6=首充粉,7=首充粉,8=首充粉")
    private Integer fansType;
    /**
     * 好友id
     */
    private Long friendId;
    /**
     * 好友下的客户ID集
     */
    @Excel(name = "粉丝ID", sort = 1)
    private String customerIds;
    /**
     * 记录日期
     */
    @Excel(name = "达成日期", sort = 3, dateFormat = "yyyy-MM-dd")
    private LocalDate recordDate;
    /**
     * 推广id(订单表)
     */
    private Long extendId;
    /**
     * 推广部门id
     */
    private Long extendDeptId;
    /**
     * 客服id(二交表)
     */
    private Long serveId;
    /**
     * 客服部门id
     */
    private Long serveDeptId;
    /**
     * 状态（0新粉 1老粉）
     */
    @Excel(name = "状态", sort = 4, readConverterExp = "0=新粉,1=老粉")
    private String status;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    private Long updateBy;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private List<Long> customerIdList;
}