package com.yooa.common.core.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.yooa.common.core.exception.ServiceException;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
public class LocalDateUtil extends LocalDateTimeUtil {

    // 获取两个时间差的每一天
    public static List<LocalDate> getDatesBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();

        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }

        return dates;
    }

    // 获取两个时间差的每小时
    public static List<LocalDateTime> getHoursBetween(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        // 将分钟和秒数设置为0
        startDateTime = startDateTime.withMinute(0).withSecond(0).withNano(0);
        endDateTime = endDateTime.withMinute(0).withSecond(0).withNano(0);

        List<LocalDateTime> dateTimes = new ArrayList<>();
        LocalDateTime currentDateTime = startDateTime;

        while (!currentDateTime.isAfter(endDateTime)) {
            dateTimes.add(currentDateTime);
            currentDateTime = currentDateTime.plusHours(1); // 每小时增加
        }

        return dateTimes;
    }

    // 获取两个时间差的每个月
    public static List<LocalDate> getMonthsBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> months = new ArrayList<>();

        // 如果 startDate 和 endTime 在同一个月
        if (startDate.getYear() == endDate.getYear() && startDate.getMonth() == endDate.getMonth()) {
            months.add(startDate.withDayOfMonth(1));
            return months;
        }

        // 确保 startDate 在月份的第一天
        LocalDate current = startDate.withDayOfMonth(1);

        // 添加 startDate 到 endTime 之间的每个月第一天
        while (current.isBefore(endDate.withDayOfMonth(1)) || current.isEqual(endDate.withDayOfMonth(1))) {
            months.add(current);
            current = current.plusMonths(1);
        }

        return months;
    }

    /**
     * 计算两个日期的相隔天数(除去星期天)
     */
    public static int daysBetweenRemoveSunday(LocalDate fromDate, LocalDate toDate) {
        // 确保 fromDate 是在 toDate 之前
        if (fromDate.isAfter(toDate)) {
            throw new ServiceException("时间参数有误!");
        }

        int numberOfDays = 0;
        LocalDate currentDate = fromDate;

        while (!currentDate.isAfter(toDate)) {
            // 检查是否是星期天
            if (currentDate.getDayOfWeek() != DayOfWeek.SUNDAY) {
                numberOfDays++;
            }
            // 移动到下一个日期
            currentDate = currentDate.plusDays(1);
        }

        return numberOfDays;
    }

    /**
     * 当前时间转为秒级时间戳
     *
     * @return 秒级时间戳
     */
    public static int toEpochSecond() {
        return Math.toIntExact(toEpochSecond(LocalDateTime.now()));
    }

    /**
     * 时间转为秒级时间戳
     *
     * @param temporalAccessor 日期
     * @return 秒级时间戳
     */
    public static int toEpochSecond(TemporalAccessor temporalAccessor) {
        return Math.toIntExact(toEpochMilli(temporalAccessor) / 1000);
    }


    /**
     * 秒级时间戳转日期
     */
    public static LocalDate epochSecondToLocalDate(int epochSecond) {
        return epochSecondToLocalDateTime(epochSecond).toLocalDate();
    }

    /**
     * 秒级时间戳转时间
     */
    public static LocalDateTime epochSecondToLocalDateTime(int epochSecond) {
        return of(epochSecond * 1000L);
    }

    /**
     * 秒级时间戳转时间
     */
    public static LocalDateTime epochSecondToLocalDateTime(String epochSecond) {
        return of((Convert.toInt(epochSecond)) * 1000L);
    }
}
